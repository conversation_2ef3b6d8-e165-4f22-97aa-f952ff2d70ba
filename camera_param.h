#ifndef CAMERA_PARAM_H
#define CAMERA_PARAM_H

#include <QObject>
#include <QVariantMap>
#include <QMap>
#include <linux/videodev2.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/ioctl.h>
#include <QString>
#include <QDebug>
#include <errno.h>

class CameraParams : public QObject
{
    Q_OBJECT

public:
    explicit CameraParams(QObject *parent = nullptr);
    ~CameraParams();

    //设置控制相关
    bool setControl(const QString &devicePath, int controlId, int value);
    bool getControlRange(const QString &devicePath, int controlId, int &min, int &max, int &step, int &defaultValue);
    bool getdefault(const QString &devicePath, int controlId, int &min, int &max, int &step, int &defaultValue);
    QString getControlName(int controlId);

    // 获取和设置自动白平衡
    bool getAutoWhiteBalance(const QString &devicePath, bool &isAuto);
    bool setAutoWhiteBalance(const QString &devicePath, bool isAuto);

    // 获取和设置自动曝光
    bool getAutoExposure(const QString &devicePath, bool &isAuto);
    bool setAutoExposure(const QString &devicePath, bool isAuto);

    // 获取和设置自动焦点
    bool getAutoFocus(const QString &devicePath, bool &isAuto);
    bool setAutoFocus(const QString &devicePath, bool isAuto);

    // 设备查找和格式相关函数
    QList<QPair<QString, QString>> findVideoDevices();
    QList<QPair<QString, QVariantMap>> findVideoFormats(const QString &devicePath);
    QList<QPair<QString, QVariantMap>> findVideoResolutions(const QString &devicePath, uint32_t pixelformat);
    void selectPreferredFormat(const QList<QPair<QString, QVariantMap>> &formats,
                               const QList<QPair<QString, QVariantMap>> &resolutions,
                               int &formatIndex, int &resolutionIndex);

    // 音频设备相关函数
    QList<QPair<QString, QString>> findAudioDevicesForCamera(const QString &usbInterface);

    // private:
    // 存储设备亮度设置
    QMap<QString, int> deviceBrightnessMap;
};

#endif // CAMERA_PARAM_H
