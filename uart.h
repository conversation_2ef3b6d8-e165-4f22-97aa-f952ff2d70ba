#ifndef __UART_H__
#define __UART_H__

//�궨��  
#define FALSE  -1
#define TRUE   0
#define UART_DEVICE "/dev/ttyS10"

typedef unsigned char u8;
typedef unsigned short int u16;

int UART_Open(int fd,char* port);
void UART_Close(int fd);
int UART_Set(int fd,int speed,int flow_ctrl,int databits,int stopbits,int parity); 
int UART_Init(int fd, int speed,int flow_ctrl,int databits,int stopbits,int parity);

int UART_Recv(int fd, char *rcv_buf,int data_len);
int UART_Send(int fd, char *send_buf,int data_len);

#endif

