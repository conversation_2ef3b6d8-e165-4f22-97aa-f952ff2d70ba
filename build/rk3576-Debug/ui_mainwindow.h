/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 5.15.11
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QFrame>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QListView>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QScrollArea>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QStackedWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralwidget;
    QGridLayout *gridLayout_4;
    QHBoxLayout *horizontalLayout_11;
    QWidget *widget_2;
    QHBoxLayout *horizontalLayout_10;
    QHBoxLayout *horizontalLayout_9;
    QSpacerItem *horizontalSpacer_11;
    QLabel *label_channel;
    QLabel *label_fbl;
    QLabel *label_fps;
    QLabel *label_BM;
    QLabel *label_ML;
    QLabel *label_storage;
    QSpacerItem *horizontalSpacer_12;
    QWidget *recordingIndicatorWidget;
    QHBoxLayout *horizontalLayout_13;
    QHBoxLayout *horizontalLayout_12;
    QLabel *recordingDotLabel;
    QLabel *recordingTimeLabel;
    QSpacerItem *verticalSpacer;
    QSpacerItem *horizontalSpacer_5;
    QVBoxLayout *verticalLayout_3;
    QStackedWidget *stackedWidget;
    QWidget *menu;
    QGridLayout *gridLayout_3;
    QVBoxLayout *verticalLayout_4;
    QHBoxLayout *horizontalLayout_3;
    QLabel *main_menu;
    QSpacerItem *horizontalSpacer_3;
    QFrame *line;
    QHBoxLayout *horizontalLayout_2;
    QVBoxLayout *verticalLayout_2;
    QPushButton *Camera;
    QPushButton *Recordset;
    QPushButton *Filemanage;
    QPushButton *Systemset;
    QSpacerItem *horizontalSpacer;
    QSpacerItem *verticalSpacer_7;
    QWidget *page_imageparame;
    QVBoxLayout *verticalLayout_8;
    QVBoxLayout *verticalLayout_7;
    QVBoxLayout *verticalLayout_5;
    QHBoxLayout *horizontalLayout_14;
    QLabel *camera;
    QSpacerItem *horizontalSpacer_13;
    QFrame *line_4;
    QHBoxLayout *horizontalLayout_15;
    QVBoxLayout *verticalLayout_6;
    QPushButton *Camera_UVC;
    QPushButton *Camera_HDMI;
    QSpacerItem *horizontalSpacer_14;
    QSpacerItem *verticalSpacer_3;
    QWidget *UVC_imageset;
    QVBoxLayout *verticalLayout_24;
    QHBoxLayout *horizontalLayout_16;
    QLabel *camera_UVC;
    QSpacerItem *horizontalSpacer_15;
    QFrame *line_5;
    QScrollArea *scrollArea_UVC;
    QWidget *scrollAreaWidgetContents_2;
    QGridLayout *gridLayout_11;
    QVBoxLayout *verticalLayout_23;
    QHBoxLayout *horizontalLayout_33;
    QPushButton *UVC_brightness;
    QSpacerItem *horizontalSpacer_32;
    QSpinBox *spinBox_UVC_brightness;
    QSpacerItem *horizontalSpacer_6;
    QHBoxLayout *horizontalLayout_34;
    QPushButton *UVC_contrast;
    QSpacerItem *horizontalSpacer_33;
    QSpinBox *spinBox_UVC_contrast;
    QSpacerItem *horizontalSpacer_7;
    QHBoxLayout *horizontalLayout_35;
    QPushButton *UVC_saturation;
    QSpacerItem *horizontalSpacer_34;
    QSpinBox *spinBox_UVC_saturation;
    QSpacerItem *horizontalSpacer_8;
    QHBoxLayout *horizontalLayout_36;
    QPushButton *UVC_hue;
    QSpacerItem *horizontalSpacer_35;
    QSpinBox *spinBox_UVC_hue;
    QSpacerItem *horizontalSpacer_9;
    QHBoxLayout *horizontalLayout_37;
    QPushButton *UVC_exposure_auto;
    QSpacerItem *horizontalSpacer_36;
    QCheckBox *checkBox_UVC_exposure;
    QSpacerItem *horizontalSpacer_10;
    QHBoxLayout *horizontalLayout_38;
    QPushButton *UVC_exposure;
    QSpacerItem *horizontalSpacer_37;
    QSpinBox *spinBox_UVC_exposure;
    QSpacerItem *horizontalSpacer_48;
    QHBoxLayout *horizontalLayout_39;
    QPushButton *UVC_white_auto;
    QSpacerItem *horizontalSpacer_38;
    QCheckBox *checkBox_UVC_white;
    QSpacerItem *horizontalSpacer_49;
    QHBoxLayout *horizontalLayout_40;
    QPushButton *UVC_white;
    QSpacerItem *horizontalSpacer_39;
    QSpinBox *spinBox__UVC_white;
    QSpacerItem *horizontalSpacer_58;
    QWidget *HDMI_imageset;
    QVBoxLayout *verticalLayout_26;
    QHBoxLayout *horizontalLayout_17;
    QLabel *camera_HDMI;
    QSpacerItem *horizontalSpacer_16;
    QFrame *line_6;
    QScrollArea *scrollArea_HDMI;
    QWidget *scrollAreaWidgetContents_3;
    QGridLayout *gridLayout_12;
    QVBoxLayout *verticalLayout_25;
    QHBoxLayout *horizontalLayout_41;
    QPushButton *HDMI_brightness;
    QSpacerItem *horizontalSpacer_40;
    QSpinBox *spinBox_HDMI_brightness;
    QSpacerItem *horizontalSpacer_50;
    QHBoxLayout *horizontalLayout_42;
    QPushButton *HDMI_contrast;
    QSpacerItem *horizontalSpacer_41;
    QSpinBox *spinBox_HDMI_contrast;
    QSpacerItem *horizontalSpacer_51;
    QHBoxLayout *horizontalLayout_43;
    QPushButton *HDMI_saturation;
    QSpacerItem *horizontalSpacer_42;
    QSpinBox *spinBox_HDMI_saturation;
    QSpacerItem *horizontalSpacer_52;
    QHBoxLayout *horizontalLayout_44;
    QPushButton *HDMI_hue;
    QSpacerItem *horizontalSpacer_43;
    QSpinBox *spinBox_HDMI_hue;
    QSpacerItem *horizontalSpacer_53;
    QHBoxLayout *horizontalLayout_45;
    QPushButton *HDMI_exposure_auto;
    QSpacerItem *horizontalSpacer_44;
    QCheckBox *checkBox_HDMI_exposure_auto;
    QSpacerItem *horizontalSpacer_54;
    QHBoxLayout *horizontalLayout_46;
    QPushButton *HDMI_exposure;
    QSpacerItem *horizontalSpacer_45;
    QSpinBox *spinBox_HDMI_exposure;
    QSpacerItem *horizontalSpacer_55;
    QHBoxLayout *horizontalLayout_47;
    QPushButton *HDMI_white_auto;
    QSpacerItem *horizontalSpacer_46;
    QCheckBox *checkBox_HDMI_white;
    QSpacerItem *horizontalSpacer_56;
    QHBoxLayout *horizontalLayout_48;
    QPushButton *HDMI_white;
    QSpacerItem *horizontalSpacer_47;
    QSpinBox *spinBox_HDMI_white;
    QSpacerItem *horizontalSpacer_57;
    QWidget *page_recordset;
    QVBoxLayout *verticalLayout_12;
    QVBoxLayout *verticalLayout_9;
    QVBoxLayout *verticalLayout_10;
    QHBoxLayout *horizontalLayout_18;
    QLabel *recordset_1;
    QSpacerItem *horizontalSpacer_17;
    QFrame *line_7;
    QHBoxLayout *horizontalLayout_19;
    QVBoxLayout *verticalLayout_11;
    QPushButton *record_UVC;
    QPushButton *record_HDMI;
    QSpacerItem *horizontalSpacer_18;
    QSpacerItem *verticalSpacer_4;
    QWidget *page_recordset_2;
    QVBoxLayout *verticalLayout_15;
    QHBoxLayout *horizontalLayout_20;
    QLabel *recordset_2;
    QSpacerItem *horizontalSpacer_19;
    QFrame *line_8;
    QScrollArea *scrollArea;
    QWidget *scrollAreaWidgetContents;
    QVBoxLayout *verticalLayout_14;
    QHBoxLayout *horizontalLayout_21;
    QPushButton *record_fbl;
    QSpacerItem *horizontalSpacer_20;
    QLabel *label_choose_fbl;
    QHBoxLayout *horizontalLayout_22;
    QPushButton *record_fps;
    QSpacerItem *horizontalSpacer_21;
    QLabel *label_choose_fps;
    QHBoxLayout *horizontalLayout_23;
    QPushButton *record_encode;
    QSpacerItem *horizontalSpacer_22;
    QLabel *label_choose_encode;
    QHBoxLayout *horizontalLayout_24;
    QPushButton *record_quality;
    QSpacerItem *horizontalSpacer_23;
    QLabel *label_choose_quality;
    QHBoxLayout *horizontalLayout_25;
    QPushButton *record_ml;
    QSpacerItem *horizontalSpacer_24;
    QHBoxLayout *horizontalLayout_26;
    QPushButton *record_storage;
    QSpacerItem *horizontalSpacer_25;
    QLabel *laebl_choose_storage;
    QVBoxLayout *verticalLayout_13;
    QHBoxLayout *horizontalLayout_27;
    QPushButton *start_recordingaudioset;
    QSpacerItem *horizontalSpacer_26;
    QLabel *laebl_choose_audio;
    QHBoxLayout *horizontalLayout_28;
    QPushButton *start_recordingaudioopen;
    QSpacerItem *horizontalSpacer_27;
    QLabel *laebl_audioopen;
    QWidget *page_filemanage;
    QVBoxLayout *verticalLayout_19;
    QVBoxLayout *verticalLayout_16;
    QVBoxLayout *verticalLayout_17;
    QHBoxLayout *horizontalLayout_29;
    QLabel *filemanage;
    QSpacerItem *horizontalSpacer_28;
    QFrame *line_9;
    QHBoxLayout *horizontalLayout_30;
    QVBoxLayout *verticalLayout_18;
    QPushButton *file_picture;
    QPushButton *file_video;
    QSpacerItem *horizontalSpacer_29;
    QSpacerItem *verticalSpacer_5;
    QWidget *viewpictures;
    QGridLayout *gridLayout_9;
    QListView *listView_viewpictures;
    QWidget *videofile;
    QGridLayout *gridLayout_8;
    QListView *listView_videofile;
    QWidget *page_systemset;
    QWidget *layoutWidget_11;
    QVBoxLayout *verticalLayout_20;
    QVBoxLayout *verticalLayout_21;
    QHBoxLayout *horizontalLayout_31;
    QLabel *systemset;
    QSpacerItem *horizontalSpacer_30;
    QFrame *line_10;
    QHBoxLayout *horizontalLayout_32;
    QVBoxLayout *verticalLayout_22;
    QPushButton *camera_4;
    QPushButton *custom_4;
    QSpacerItem *horizontalSpacer_31;
    QSpacerItem *verticalSpacer_6;
    QWidget *help_menu;
    QGridLayout *gridLayout_2;
    QVBoxLayout *verticalLayout;
    QFrame *line_2;
    QHBoxLayout *horizontalLayout;
    QGridLayout *gridLayout;
    QLabel *Return;
    QLabel *select;
    QLabel *reset;
    QLabel *modification;
    QSpacerItem *horizontalSpacer_2;
    QSpacerItem *horizontalSpacer_4;
    QSpacerItem *verticalSpacer_2;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName(QString::fromUtf8("MainWindow"));
        MainWindow->resize(1560, 717);
        MainWindow->setStyleSheet(QString::fromUtf8(""));
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName(QString::fromUtf8("centralwidget"));
        QSizePolicy sizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
        sizePolicy.setHorizontalStretch(0);
        sizePolicy.setVerticalStretch(0);
        sizePolicy.setHeightForWidth(centralwidget->sizePolicy().hasHeightForWidth());
        centralwidget->setSizePolicy(sizePolicy);
        centralwidget->setStyleSheet(QString::fromUtf8(""));
        gridLayout_4 = new QGridLayout(centralwidget);
        gridLayout_4->setObjectName(QString::fromUtf8("gridLayout_4"));
        horizontalLayout_11 = new QHBoxLayout();
        horizontalLayout_11->setObjectName(QString::fromUtf8("horizontalLayout_11"));
        widget_2 = new QWidget(centralwidget);
        widget_2->setObjectName(QString::fromUtf8("widget_2"));
        widget_2->setStyleSheet(QString::fromUtf8("background-color: transparent;"));
        horizontalLayout_10 = new QHBoxLayout(widget_2);
        horizontalLayout_10->setObjectName(QString::fromUtf8("horizontalLayout_10"));
        horizontalLayout_10->setContentsMargins(0, 0, 0, 0);
        horizontalLayout_9 = new QHBoxLayout();
        horizontalLayout_9->setSpacing(5);
        horizontalLayout_9->setObjectName(QString::fromUtf8("horizontalLayout_9"));
        horizontalSpacer_11 = new QSpacerItem(520, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_9->addItem(horizontalSpacer_11);

        label_channel = new QLabel(widget_2);
        label_channel->setObjectName(QString::fromUtf8("label_channel"));
        QSizePolicy sizePolicy1(QSizePolicy::Fixed, QSizePolicy::Preferred);
        sizePolicy1.setHorizontalStretch(0);
        sizePolicy1.setVerticalStretch(0);
        sizePolicy1.setHeightForWidth(label_channel->sizePolicy().hasHeightForWidth());
        label_channel->setSizePolicy(sizePolicy1);
        label_channel->setMinimumSize(QSize(140, 0));
        label_channel->setMaximumSize(QSize(140, 16777215));
        QFont font;
        font.setPointSize(17);
        label_channel->setFont(font);

        horizontalLayout_9->addWidget(label_channel);

        label_fbl = new QLabel(widget_2);
        label_fbl->setObjectName(QString::fromUtf8("label_fbl"));
        label_fbl->setMinimumSize(QSize(140, 0));
        label_fbl->setMaximumSize(QSize(140, 16777215));
        label_fbl->setFont(font);

        horizontalLayout_9->addWidget(label_fbl);

        label_fps = new QLabel(widget_2);
        label_fps->setObjectName(QString::fromUtf8("label_fps"));
        label_fps->setMinimumSize(QSize(120, 0));
        label_fps->setMaximumSize(QSize(120, 16777215));
        label_fps->setFont(font);

        horizontalLayout_9->addWidget(label_fps);

        label_BM = new QLabel(widget_2);
        label_BM->setObjectName(QString::fromUtf8("label_BM"));
        label_BM->setMinimumSize(QSize(140, 0));
        label_BM->setMaximumSize(QSize(140, 16777215));
        label_BM->setFont(font);

        horizontalLayout_9->addWidget(label_BM);

        label_ML = new QLabel(widget_2);
        label_ML->setObjectName(QString::fromUtf8("label_ML"));
        label_ML->setMinimumSize(QSize(140, 0));
        label_ML->setMaximumSize(QSize(140, 16777215));
        label_ML->setFont(font);

        horizontalLayout_9->addWidget(label_ML);

        label_storage = new QLabel(widget_2);
        label_storage->setObjectName(QString::fromUtf8("label_storage"));
        label_storage->setMinimumSize(QSize(180, 0));
        label_storage->setMaximumSize(QSize(180, 16777215));
        label_storage->setFont(font);

        horizontalLayout_9->addWidget(label_storage);


        horizontalLayout_10->addLayout(horizontalLayout_9);


        horizontalLayout_11->addWidget(widget_2);

        horizontalSpacer_12 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_11->addItem(horizontalSpacer_12);

        recordingIndicatorWidget = new QWidget(centralwidget);
        recordingIndicatorWidget->setObjectName(QString::fromUtf8("recordingIndicatorWidget"));
        recordingIndicatorWidget->setStyleSheet(QString::fromUtf8("background-color: transparent;"));
        horizontalLayout_13 = new QHBoxLayout(recordingIndicatorWidget);
        horizontalLayout_13->setSpacing(0);
        horizontalLayout_13->setObjectName(QString::fromUtf8("horizontalLayout_13"));
        horizontalLayout_13->setContentsMargins(0, 0, 6, 0);
        horizontalLayout_12 = new QHBoxLayout();
        horizontalLayout_12->setObjectName(QString::fromUtf8("horizontalLayout_12"));
        recordingDotLabel = new QLabel(recordingIndicatorWidget);
        recordingDotLabel->setObjectName(QString::fromUtf8("recordingDotLabel"));
        recordingDotLabel->setMinimumSize(QSize(20, 20));
        recordingDotLabel->setMaximumSize(QSize(20, 20));
        recordingDotLabel->setStyleSheet(QString::fromUtf8("background-color: red;\n"
"border-radius: 10px;"));

        horizontalLayout_12->addWidget(recordingDotLabel);

        recordingTimeLabel = new QLabel(recordingIndicatorWidget);
        recordingTimeLabel->setObjectName(QString::fromUtf8("recordingTimeLabel"));
        recordingTimeLabel->setMinimumSize(QSize(0, 0));
        recordingTimeLabel->setMaximumSize(QSize(16777215, 16777215));
        QFont font1;
        font1.setPointSize(16);
        font1.setBold(true);
        recordingTimeLabel->setFont(font1);
        recordingTimeLabel->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: transparent;"));

        horizontalLayout_12->addWidget(recordingTimeLabel);


        horizontalLayout_13->addLayout(horizontalLayout_12);


        horizontalLayout_11->addWidget(recordingIndicatorWidget);


        gridLayout_4->addLayout(horizontalLayout_11, 0, 0, 1, 3);

        verticalSpacer = new QSpacerItem(20, 22, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_4->addItem(verticalSpacer, 1, 1, 1, 1);

        horizontalSpacer_5 = new QSpacerItem(320, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_4->addItem(horizontalSpacer_5, 2, 0, 1, 1);

        verticalLayout_3 = new QVBoxLayout();
        verticalLayout_3->setSpacing(0);
        verticalLayout_3->setObjectName(QString::fromUtf8("verticalLayout_3"));
        stackedWidget = new QStackedWidget(centralwidget);
        stackedWidget->setObjectName(QString::fromUtf8("stackedWidget"));
        stackedWidget->setStyleSheet(QString::fromUtf8("background-color: rgba(119, 118, 123,127);"));
        menu = new QWidget();
        menu->setObjectName(QString::fromUtf8("menu"));
        menu->setStyleSheet(QString::fromUtf8("background-color: rgba(119, 118, 123,127);"));
        gridLayout_3 = new QGridLayout(menu);
        gridLayout_3->setSpacing(0);
        gridLayout_3->setObjectName(QString::fromUtf8("gridLayout_3"));
        gridLayout_3->setContentsMargins(0, 0, 0, 0);
        verticalLayout_4 = new QVBoxLayout();
        verticalLayout_4->setSpacing(10);
        verticalLayout_4->setObjectName(QString::fromUtf8("verticalLayout_4"));
        verticalLayout_4->setContentsMargins(-1, -1, -1, 10);
        horizontalLayout_3 = new QHBoxLayout();
        horizontalLayout_3->setObjectName(QString::fromUtf8("horizontalLayout_3"));
        main_menu = new QLabel(menu);
        main_menu->setObjectName(QString::fromUtf8("main_menu"));
        main_menu->setMinimumSize(QSize(0, 60));
        QFont font2;
        font2.setPointSize(31);
        main_menu->setFont(font2);
        main_menu->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        horizontalLayout_3->addWidget(main_menu);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_3->addItem(horizontalSpacer_3);


        verticalLayout_4->addLayout(horizontalLayout_3);

        line = new QFrame(menu);
        line->setObjectName(QString::fromUtf8("line"));
        line->setMinimumSize(QSize(0, 0));
        line->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line->setMidLineWidth(5);
        line->setFrameShape(QFrame::HLine);
        line->setFrameShadow(QFrame::Sunken);

        verticalLayout_4->addWidget(line);

        horizontalLayout_2 = new QHBoxLayout();
        horizontalLayout_2->setObjectName(QString::fromUtf8("horizontalLayout_2"));
        verticalLayout_2 = new QVBoxLayout();
        verticalLayout_2->setObjectName(QString::fromUtf8("verticalLayout_2"));
        Camera = new QPushButton(menu);
        Camera->setObjectName(QString::fromUtf8("Camera"));
        Camera->setMinimumSize(QSize(0, 50));
        Camera->setFont(font2);
        Camera->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        Camera->setCheckable(true);
        Camera->setAutoRepeat(false);
        Camera->setAutoExclusive(false);

        verticalLayout_2->addWidget(Camera);

        Recordset = new QPushButton(menu);
        Recordset->setObjectName(QString::fromUtf8("Recordset"));
        Recordset->setMinimumSize(QSize(0, 50));
        Recordset->setFont(font2);
        Recordset->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        Recordset->setCheckable(true);

        verticalLayout_2->addWidget(Recordset);

        Filemanage = new QPushButton(menu);
        Filemanage->setObjectName(QString::fromUtf8("Filemanage"));
        Filemanage->setMinimumSize(QSize(0, 50));
        Filemanage->setFont(font2);
        Filemanage->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        Filemanage->setCheckable(true);

        verticalLayout_2->addWidget(Filemanage);

        Systemset = new QPushButton(menu);
        Systemset->setObjectName(QString::fromUtf8("Systemset"));
        Systemset->setMinimumSize(QSize(220, 50));
        Systemset->setFont(font2);
        Systemset->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        Systemset->setCheckable(true);

        verticalLayout_2->addWidget(Systemset);


        horizontalLayout_2->addLayout(verticalLayout_2);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_2->addItem(horizontalSpacer);


        verticalLayout_4->addLayout(horizontalLayout_2);

        verticalSpacer_7 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_4->addItem(verticalSpacer_7);


        gridLayout_3->addLayout(verticalLayout_4, 0, 0, 1, 1);

        stackedWidget->addWidget(menu);
        page_imageparame = new QWidget();
        page_imageparame->setObjectName(QString::fromUtf8("page_imageparame"));
        verticalLayout_8 = new QVBoxLayout(page_imageparame);
        verticalLayout_8->setObjectName(QString::fromUtf8("verticalLayout_8"));
        verticalLayout_8->setContentsMargins(0, 0, 0, 0);
        verticalLayout_7 = new QVBoxLayout();
        verticalLayout_7->setObjectName(QString::fromUtf8("verticalLayout_7"));
        verticalLayout_5 = new QVBoxLayout();
        verticalLayout_5->setObjectName(QString::fromUtf8("verticalLayout_5"));
        horizontalLayout_14 = new QHBoxLayout();
        horizontalLayout_14->setObjectName(QString::fromUtf8("horizontalLayout_14"));
        horizontalLayout_14->setSizeConstraint(QLayout::SetFixedSize);
        camera = new QLabel(page_imageparame);
        camera->setObjectName(QString::fromUtf8("camera"));
        QSizePolicy sizePolicy2(QSizePolicy::Preferred, QSizePolicy::Fixed);
        sizePolicy2.setHorizontalStretch(0);
        sizePolicy2.setVerticalStretch(0);
        sizePolicy2.setHeightForWidth(camera->sizePolicy().hasHeightForWidth());
        camera->setSizePolicy(sizePolicy2);
        camera->setMinimumSize(QSize(0, 60));
        camera->setMaximumSize(QSize(16777215, 60));
        camera->setFont(font2);
        camera->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        horizontalLayout_14->addWidget(camera);

        horizontalSpacer_13 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_14->addItem(horizontalSpacer_13);


        verticalLayout_5->addLayout(horizontalLayout_14);

        line_4 = new QFrame(page_imageparame);
        line_4->setObjectName(QString::fromUtf8("line_4"));
        line_4->setMinimumSize(QSize(0, 0));
        line_4->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line_4->setMidLineWidth(5);
        line_4->setFrameShape(QFrame::HLine);
        line_4->setFrameShadow(QFrame::Sunken);

        verticalLayout_5->addWidget(line_4);

        horizontalLayout_15 = new QHBoxLayout();
        horizontalLayout_15->setObjectName(QString::fromUtf8("horizontalLayout_15"));
        verticalLayout_6 = new QVBoxLayout();
        verticalLayout_6->setObjectName(QString::fromUtf8("verticalLayout_6"));
        Camera_UVC = new QPushButton(page_imageparame);
        Camera_UVC->setObjectName(QString::fromUtf8("Camera_UVC"));
        Camera_UVC->setMinimumSize(QSize(0, 50));
        Camera_UVC->setFont(font2);
        Camera_UVC->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        Camera_UVC->setCheckable(true);
        Camera_UVC->setAutoRepeat(false);
        Camera_UVC->setAutoExclusive(false);

        verticalLayout_6->addWidget(Camera_UVC);

        Camera_HDMI = new QPushButton(page_imageparame);
        Camera_HDMI->setObjectName(QString::fromUtf8("Camera_HDMI"));
        Camera_HDMI->setMinimumSize(QSize(0, 50));
        Camera_HDMI->setFont(font2);
        Camera_HDMI->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        Camera_HDMI->setCheckable(true);
        Camera_HDMI->setChecked(false);

        verticalLayout_6->addWidget(Camera_HDMI);


        horizontalLayout_15->addLayout(verticalLayout_6);

        horizontalSpacer_14 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_15->addItem(horizontalSpacer_14);


        verticalLayout_5->addLayout(horizontalLayout_15);


        verticalLayout_7->addLayout(verticalLayout_5);

        verticalSpacer_3 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_7->addItem(verticalSpacer_3);


        verticalLayout_8->addLayout(verticalLayout_7);

        stackedWidget->addWidget(page_imageparame);
        UVC_imageset = new QWidget();
        UVC_imageset->setObjectName(QString::fromUtf8("UVC_imageset"));
        verticalLayout_24 = new QVBoxLayout(UVC_imageset);
        verticalLayout_24->setObjectName(QString::fromUtf8("verticalLayout_24"));
        verticalLayout_24->setContentsMargins(0, 0, 0, 0);
        horizontalLayout_16 = new QHBoxLayout();
        horizontalLayout_16->setObjectName(QString::fromUtf8("horizontalLayout_16"));
        horizontalLayout_16->setSizeConstraint(QLayout::SetFixedSize);
        camera_UVC = new QLabel(UVC_imageset);
        camera_UVC->setObjectName(QString::fromUtf8("camera_UVC"));
        sizePolicy2.setHeightForWidth(camera_UVC->sizePolicy().hasHeightForWidth());
        camera_UVC->setSizePolicy(sizePolicy2);
        camera_UVC->setMinimumSize(QSize(0, 60));
        camera_UVC->setMaximumSize(QSize(16777215, 60));
        camera_UVC->setFont(font2);
        camera_UVC->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        horizontalLayout_16->addWidget(camera_UVC);

        horizontalSpacer_15 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_16->addItem(horizontalSpacer_15);


        verticalLayout_24->addLayout(horizontalLayout_16);

        line_5 = new QFrame(UVC_imageset);
        line_5->setObjectName(QString::fromUtf8("line_5"));
        line_5->setMinimumSize(QSize(0, 0));
        line_5->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line_5->setMidLineWidth(5);
        line_5->setFrameShape(QFrame::HLine);
        line_5->setFrameShadow(QFrame::Sunken);

        verticalLayout_24->addWidget(line_5);

        scrollArea_UVC = new QScrollArea(UVC_imageset);
        scrollArea_UVC->setObjectName(QString::fromUtf8("scrollArea_UVC"));
        scrollArea_UVC->setMinimumSize(QSize(0, 0));
        scrollArea_UVC->setStyleSheet(QString::fromUtf8(""));
        scrollArea_UVC->setLineWidth(1);
        scrollArea_UVC->setMidLineWidth(0);
        scrollArea_UVC->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        scrollArea_UVC->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        scrollArea_UVC->setWidgetResizable(true);
        scrollAreaWidgetContents_2 = new QWidget();
        scrollAreaWidgetContents_2->setObjectName(QString::fromUtf8("scrollAreaWidgetContents_2"));
        scrollAreaWidgetContents_2->setGeometry(QRect(0, 0, 867, 460));
        scrollAreaWidgetContents_2->setStyleSheet(QString::fromUtf8(""));
        gridLayout_11 = new QGridLayout(scrollAreaWidgetContents_2);
        gridLayout_11->setObjectName(QString::fromUtf8("gridLayout_11"));
        gridLayout_11->setContentsMargins(0, 0, 0, 0);
        verticalLayout_23 = new QVBoxLayout();
        verticalLayout_23->setObjectName(QString::fromUtf8("verticalLayout_23"));
        horizontalLayout_33 = new QHBoxLayout();
        horizontalLayout_33->setSpacing(6);
        horizontalLayout_33->setObjectName(QString::fromUtf8("horizontalLayout_33"));
        UVC_brightness = new QPushButton(scrollAreaWidgetContents_2);
        UVC_brightness->setObjectName(QString::fromUtf8("UVC_brightness"));
        UVC_brightness->setMinimumSize(QSize(220, 50));
        UVC_brightness->setMaximumSize(QSize(220, 50));
        UVC_brightness->setFont(font2);
        UVC_brightness->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        UVC_brightness->setCheckable(true);

        horizontalLayout_33->addWidget(UVC_brightness);

        horizontalSpacer_32 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_33->addItem(horizontalSpacer_32);

        spinBox_UVC_brightness = new QSpinBox(scrollAreaWidgetContents_2);
        spinBox_UVC_brightness->setObjectName(QString::fromUtf8("spinBox_UVC_brightness"));
        spinBox_UVC_brightness->setMinimumSize(QSize(0, 0));
        spinBox_UVC_brightness->setMaximumSize(QSize(100, 30));
        QFont font3;
        font3.setPointSize(11);
        spinBox_UVC_brightness->setFont(font3);
        spinBox_UVC_brightness->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));
        spinBox_UVC_brightness->setAlignment(Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter);

        horizontalLayout_33->addWidget(spinBox_UVC_brightness);

        horizontalSpacer_6 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_33->addItem(horizontalSpacer_6);


        verticalLayout_23->addLayout(horizontalLayout_33);

        horizontalLayout_34 = new QHBoxLayout();
        horizontalLayout_34->setObjectName(QString::fromUtf8("horizontalLayout_34"));
        UVC_contrast = new QPushButton(scrollAreaWidgetContents_2);
        UVC_contrast->setObjectName(QString::fromUtf8("UVC_contrast"));
        UVC_contrast->setMinimumSize(QSize(220, 50));
        UVC_contrast->setMaximumSize(QSize(220, 50));
        UVC_contrast->setFont(font2);
        UVC_contrast->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        UVC_contrast->setCheckable(true);

        horizontalLayout_34->addWidget(UVC_contrast);

        horizontalSpacer_33 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_34->addItem(horizontalSpacer_33);

        spinBox_UVC_contrast = new QSpinBox(scrollAreaWidgetContents_2);
        spinBox_UVC_contrast->setObjectName(QString::fromUtf8("spinBox_UVC_contrast"));
        spinBox_UVC_contrast->setMaximumSize(QSize(100, 30));
        spinBox_UVC_contrast->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_34->addWidget(spinBox_UVC_contrast);

        horizontalSpacer_7 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_34->addItem(horizontalSpacer_7);


        verticalLayout_23->addLayout(horizontalLayout_34);

        horizontalLayout_35 = new QHBoxLayout();
        horizontalLayout_35->setObjectName(QString::fromUtf8("horizontalLayout_35"));
        UVC_saturation = new QPushButton(scrollAreaWidgetContents_2);
        UVC_saturation->setObjectName(QString::fromUtf8("UVC_saturation"));
        UVC_saturation->setMinimumSize(QSize(220, 50));
        UVC_saturation->setMaximumSize(QSize(220, 50));
        UVC_saturation->setFont(font2);
        UVC_saturation->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        UVC_saturation->setCheckable(true);

        horizontalLayout_35->addWidget(UVC_saturation);

        horizontalSpacer_34 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_35->addItem(horizontalSpacer_34);

        spinBox_UVC_saturation = new QSpinBox(scrollAreaWidgetContents_2);
        spinBox_UVC_saturation->setObjectName(QString::fromUtf8("spinBox_UVC_saturation"));
        spinBox_UVC_saturation->setMaximumSize(QSize(100, 30));
        spinBox_UVC_saturation->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_35->addWidget(spinBox_UVC_saturation);

        horizontalSpacer_8 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_35->addItem(horizontalSpacer_8);


        verticalLayout_23->addLayout(horizontalLayout_35);

        horizontalLayout_36 = new QHBoxLayout();
        horizontalLayout_36->setObjectName(QString::fromUtf8("horizontalLayout_36"));
        UVC_hue = new QPushButton(scrollAreaWidgetContents_2);
        UVC_hue->setObjectName(QString::fromUtf8("UVC_hue"));
        UVC_hue->setMinimumSize(QSize(220, 50));
        UVC_hue->setMaximumSize(QSize(220, 50));
        UVC_hue->setFont(font2);
        UVC_hue->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        UVC_hue->setCheckable(true);

        horizontalLayout_36->addWidget(UVC_hue);

        horizontalSpacer_35 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_36->addItem(horizontalSpacer_35);

        spinBox_UVC_hue = new QSpinBox(scrollAreaWidgetContents_2);
        spinBox_UVC_hue->setObjectName(QString::fromUtf8("spinBox_UVC_hue"));
        spinBox_UVC_hue->setMaximumSize(QSize(100, 30));
        spinBox_UVC_hue->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_36->addWidget(spinBox_UVC_hue);

        horizontalSpacer_9 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_36->addItem(horizontalSpacer_9);


        verticalLayout_23->addLayout(horizontalLayout_36);

        horizontalLayout_37 = new QHBoxLayout();
        horizontalLayout_37->setObjectName(QString::fromUtf8("horizontalLayout_37"));
        UVC_exposure_auto = new QPushButton(scrollAreaWidgetContents_2);
        UVC_exposure_auto->setObjectName(QString::fromUtf8("UVC_exposure_auto"));
        UVC_exposure_auto->setMinimumSize(QSize(220, 50));
        UVC_exposure_auto->setMaximumSize(QSize(220, 50));
        UVC_exposure_auto->setFont(font2);
        UVC_exposure_auto->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        UVC_exposure_auto->setCheckable(true);

        horizontalLayout_37->addWidget(UVC_exposure_auto);

        horizontalSpacer_36 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_37->addItem(horizontalSpacer_36);

        checkBox_UVC_exposure = new QCheckBox(scrollAreaWidgetContents_2);
        checkBox_UVC_exposure->setObjectName(QString::fromUtf8("checkBox_UVC_exposure"));
        QSizePolicy sizePolicy3(QSizePolicy::Fixed, QSizePolicy::Fixed);
        sizePolicy3.setHorizontalStretch(0);
        sizePolicy3.setVerticalStretch(0);
        sizePolicy3.setHeightForWidth(checkBox_UVC_exposure->sizePolicy().hasHeightForWidth());
        checkBox_UVC_exposure->setSizePolicy(sizePolicy3);
        checkBox_UVC_exposure->setMaximumSize(QSize(100, 30));
        checkBox_UVC_exposure->setStyleSheet(QString::fromUtf8(""));
        checkBox_UVC_exposure->setIconSize(QSize(30, 30));
        checkBox_UVC_exposure->setChecked(true);

        horizontalLayout_37->addWidget(checkBox_UVC_exposure);

        horizontalSpacer_10 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_37->addItem(horizontalSpacer_10);


        verticalLayout_23->addLayout(horizontalLayout_37);

        horizontalLayout_38 = new QHBoxLayout();
        horizontalLayout_38->setObjectName(QString::fromUtf8("horizontalLayout_38"));
        UVC_exposure = new QPushButton(scrollAreaWidgetContents_2);
        UVC_exposure->setObjectName(QString::fromUtf8("UVC_exposure"));
        UVC_exposure->setMinimumSize(QSize(220, 50));
        UVC_exposure->setMaximumSize(QSize(220, 50));
        UVC_exposure->setFont(font2);
        UVC_exposure->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        UVC_exposure->setCheckable(true);

        horizontalLayout_38->addWidget(UVC_exposure);

        horizontalSpacer_37 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_38->addItem(horizontalSpacer_37);

        spinBox_UVC_exposure = new QSpinBox(scrollAreaWidgetContents_2);
        spinBox_UVC_exposure->setObjectName(QString::fromUtf8("spinBox_UVC_exposure"));
        spinBox_UVC_exposure->setMaximumSize(QSize(100, 30));
        spinBox_UVC_exposure->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_38->addWidget(spinBox_UVC_exposure);

        horizontalSpacer_48 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_38->addItem(horizontalSpacer_48);


        verticalLayout_23->addLayout(horizontalLayout_38);

        horizontalLayout_39 = new QHBoxLayout();
        horizontalLayout_39->setObjectName(QString::fromUtf8("horizontalLayout_39"));
        UVC_white_auto = new QPushButton(scrollAreaWidgetContents_2);
        UVC_white_auto->setObjectName(QString::fromUtf8("UVC_white_auto"));
        UVC_white_auto->setMinimumSize(QSize(220, 50));
        UVC_white_auto->setMaximumSize(QSize(220, 50));
        UVC_white_auto->setFont(font2);
        UVC_white_auto->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        UVC_white_auto->setCheckable(true);

        horizontalLayout_39->addWidget(UVC_white_auto);

        horizontalSpacer_38 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_39->addItem(horizontalSpacer_38);

        checkBox_UVC_white = new QCheckBox(scrollAreaWidgetContents_2);
        checkBox_UVC_white->setObjectName(QString::fromUtf8("checkBox_UVC_white"));
        sizePolicy3.setHeightForWidth(checkBox_UVC_white->sizePolicy().hasHeightForWidth());
        checkBox_UVC_white->setSizePolicy(sizePolicy3);
        checkBox_UVC_white->setMaximumSize(QSize(100, 30));
        checkBox_UVC_white->setStyleSheet(QString::fromUtf8(""));
        checkBox_UVC_white->setIconSize(QSize(30, 30));
        checkBox_UVC_white->setChecked(true);

        horizontalLayout_39->addWidget(checkBox_UVC_white);

        horizontalSpacer_49 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_39->addItem(horizontalSpacer_49);


        verticalLayout_23->addLayout(horizontalLayout_39);

        horizontalLayout_40 = new QHBoxLayout();
        horizontalLayout_40->setObjectName(QString::fromUtf8("horizontalLayout_40"));
        UVC_white = new QPushButton(scrollAreaWidgetContents_2);
        UVC_white->setObjectName(QString::fromUtf8("UVC_white"));
        UVC_white->setMinimumSize(QSize(220, 50));
        UVC_white->setMaximumSize(QSize(220, 50));
        UVC_white->setFont(font2);
        UVC_white->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        UVC_white->setCheckable(true);

        horizontalLayout_40->addWidget(UVC_white);

        horizontalSpacer_39 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_40->addItem(horizontalSpacer_39);

        spinBox__UVC_white = new QSpinBox(scrollAreaWidgetContents_2);
        spinBox__UVC_white->setObjectName(QString::fromUtf8("spinBox__UVC_white"));
        spinBox__UVC_white->setMaximumSize(QSize(100, 30));
        spinBox__UVC_white->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_40->addWidget(spinBox__UVC_white);

        horizontalSpacer_58 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_40->addItem(horizontalSpacer_58);


        verticalLayout_23->addLayout(horizontalLayout_40);


        gridLayout_11->addLayout(verticalLayout_23, 0, 0, 1, 1);

        scrollArea_UVC->setWidget(scrollAreaWidgetContents_2);

        verticalLayout_24->addWidget(scrollArea_UVC);

        stackedWidget->addWidget(UVC_imageset);
        HDMI_imageset = new QWidget();
        HDMI_imageset->setObjectName(QString::fromUtf8("HDMI_imageset"));
        verticalLayout_26 = new QVBoxLayout(HDMI_imageset);
        verticalLayout_26->setObjectName(QString::fromUtf8("verticalLayout_26"));
        verticalLayout_26->setContentsMargins(0, 0, 0, 0);
        horizontalLayout_17 = new QHBoxLayout();
        horizontalLayout_17->setObjectName(QString::fromUtf8("horizontalLayout_17"));
        horizontalLayout_17->setSizeConstraint(QLayout::SetFixedSize);
        camera_HDMI = new QLabel(HDMI_imageset);
        camera_HDMI->setObjectName(QString::fromUtf8("camera_HDMI"));
        sizePolicy2.setHeightForWidth(camera_HDMI->sizePolicy().hasHeightForWidth());
        camera_HDMI->setSizePolicy(sizePolicy2);
        camera_HDMI->setMinimumSize(QSize(0, 60));
        camera_HDMI->setMaximumSize(QSize(16777215, 60));
        camera_HDMI->setFont(font2);
        camera_HDMI->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        horizontalLayout_17->addWidget(camera_HDMI);

        horizontalSpacer_16 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_17->addItem(horizontalSpacer_16);


        verticalLayout_26->addLayout(horizontalLayout_17);

        line_6 = new QFrame(HDMI_imageset);
        line_6->setObjectName(QString::fromUtf8("line_6"));
        line_6->setMinimumSize(QSize(0, 0));
        line_6->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line_6->setMidLineWidth(5);
        line_6->setFrameShape(QFrame::HLine);
        line_6->setFrameShadow(QFrame::Sunken);

        verticalLayout_26->addWidget(line_6);

        scrollArea_HDMI = new QScrollArea(HDMI_imageset);
        scrollArea_HDMI->setObjectName(QString::fromUtf8("scrollArea_HDMI"));
        scrollArea_HDMI->setMinimumSize(QSize(0, 0));
        scrollArea_HDMI->setLineWidth(1);
        scrollArea_HDMI->setMidLineWidth(0);
        scrollArea_HDMI->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        scrollArea_HDMI->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
        scrollArea_HDMI->setWidgetResizable(true);
        scrollAreaWidgetContents_3 = new QWidget();
        scrollAreaWidgetContents_3->setObjectName(QString::fromUtf8("scrollAreaWidgetContents_3"));
        scrollAreaWidgetContents_3->setGeometry(QRect(0, 0, 867, 460));
        gridLayout_12 = new QGridLayout(scrollAreaWidgetContents_3);
        gridLayout_12->setObjectName(QString::fromUtf8("gridLayout_12"));
        gridLayout_12->setContentsMargins(0, 0, 0, 0);
        verticalLayout_25 = new QVBoxLayout();
        verticalLayout_25->setObjectName(QString::fromUtf8("verticalLayout_25"));
        horizontalLayout_41 = new QHBoxLayout();
        horizontalLayout_41->setObjectName(QString::fromUtf8("horizontalLayout_41"));
        HDMI_brightness = new QPushButton(scrollAreaWidgetContents_3);
        HDMI_brightness->setObjectName(QString::fromUtf8("HDMI_brightness"));
        HDMI_brightness->setMinimumSize(QSize(220, 50));
        HDMI_brightness->setMaximumSize(QSize(220, 50));
        HDMI_brightness->setFont(font2);
        HDMI_brightness->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        HDMI_brightness->setCheckable(true);

        horizontalLayout_41->addWidget(HDMI_brightness);

        horizontalSpacer_40 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_41->addItem(horizontalSpacer_40);

        spinBox_HDMI_brightness = new QSpinBox(scrollAreaWidgetContents_3);
        spinBox_HDMI_brightness->setObjectName(QString::fromUtf8("spinBox_HDMI_brightness"));
        spinBox_HDMI_brightness->setMinimumSize(QSize(0, 0));
        spinBox_HDMI_brightness->setMaximumSize(QSize(100, 30));
        spinBox_HDMI_brightness->setFont(font3);
        spinBox_HDMI_brightness->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));
        spinBox_HDMI_brightness->setAlignment(Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter);

        horizontalLayout_41->addWidget(spinBox_HDMI_brightness);

        horizontalSpacer_50 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_41->addItem(horizontalSpacer_50);


        verticalLayout_25->addLayout(horizontalLayout_41);

        horizontalLayout_42 = new QHBoxLayout();
        horizontalLayout_42->setObjectName(QString::fromUtf8("horizontalLayout_42"));
        HDMI_contrast = new QPushButton(scrollAreaWidgetContents_3);
        HDMI_contrast->setObjectName(QString::fromUtf8("HDMI_contrast"));
        HDMI_contrast->setMinimumSize(QSize(220, 50));
        HDMI_contrast->setMaximumSize(QSize(220, 50));
        HDMI_contrast->setFont(font2);
        HDMI_contrast->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        HDMI_contrast->setCheckable(true);

        horizontalLayout_42->addWidget(HDMI_contrast);

        horizontalSpacer_41 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_42->addItem(horizontalSpacer_41);

        spinBox_HDMI_contrast = new QSpinBox(scrollAreaWidgetContents_3);
        spinBox_HDMI_contrast->setObjectName(QString::fromUtf8("spinBox_HDMI_contrast"));
        spinBox_HDMI_contrast->setMaximumSize(QSize(100, 30));
        spinBox_HDMI_contrast->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_42->addWidget(spinBox_HDMI_contrast);

        horizontalSpacer_51 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_42->addItem(horizontalSpacer_51);


        verticalLayout_25->addLayout(horizontalLayout_42);

        horizontalLayout_43 = new QHBoxLayout();
        horizontalLayout_43->setObjectName(QString::fromUtf8("horizontalLayout_43"));
        HDMI_saturation = new QPushButton(scrollAreaWidgetContents_3);
        HDMI_saturation->setObjectName(QString::fromUtf8("HDMI_saturation"));
        HDMI_saturation->setMinimumSize(QSize(220, 50));
        HDMI_saturation->setMaximumSize(QSize(220, 50));
        HDMI_saturation->setFont(font2);
        HDMI_saturation->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        HDMI_saturation->setCheckable(true);

        horizontalLayout_43->addWidget(HDMI_saturation);

        horizontalSpacer_42 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_43->addItem(horizontalSpacer_42);

        spinBox_HDMI_saturation = new QSpinBox(scrollAreaWidgetContents_3);
        spinBox_HDMI_saturation->setObjectName(QString::fromUtf8("spinBox_HDMI_saturation"));
        spinBox_HDMI_saturation->setMaximumSize(QSize(100, 30));
        spinBox_HDMI_saturation->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_43->addWidget(spinBox_HDMI_saturation);

        horizontalSpacer_52 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_43->addItem(horizontalSpacer_52);


        verticalLayout_25->addLayout(horizontalLayout_43);

        horizontalLayout_44 = new QHBoxLayout();
        horizontalLayout_44->setObjectName(QString::fromUtf8("horizontalLayout_44"));
        HDMI_hue = new QPushButton(scrollAreaWidgetContents_3);
        HDMI_hue->setObjectName(QString::fromUtf8("HDMI_hue"));
        HDMI_hue->setMinimumSize(QSize(220, 50));
        HDMI_hue->setMaximumSize(QSize(220, 50));
        HDMI_hue->setFont(font2);
        HDMI_hue->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        HDMI_hue->setCheckable(true);

        horizontalLayout_44->addWidget(HDMI_hue);

        horizontalSpacer_43 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_44->addItem(horizontalSpacer_43);

        spinBox_HDMI_hue = new QSpinBox(scrollAreaWidgetContents_3);
        spinBox_HDMI_hue->setObjectName(QString::fromUtf8("spinBox_HDMI_hue"));
        spinBox_HDMI_hue->setMaximumSize(QSize(100, 30));
        spinBox_HDMI_hue->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_44->addWidget(spinBox_HDMI_hue);

        horizontalSpacer_53 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_44->addItem(horizontalSpacer_53);


        verticalLayout_25->addLayout(horizontalLayout_44);

        horizontalLayout_45 = new QHBoxLayout();
        horizontalLayout_45->setObjectName(QString::fromUtf8("horizontalLayout_45"));
        HDMI_exposure_auto = new QPushButton(scrollAreaWidgetContents_3);
        HDMI_exposure_auto->setObjectName(QString::fromUtf8("HDMI_exposure_auto"));
        HDMI_exposure_auto->setMinimumSize(QSize(220, 50));
        HDMI_exposure_auto->setMaximumSize(QSize(220, 50));
        HDMI_exposure_auto->setFont(font2);
        HDMI_exposure_auto->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        HDMI_exposure_auto->setCheckable(true);

        horizontalLayout_45->addWidget(HDMI_exposure_auto);

        horizontalSpacer_44 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_45->addItem(horizontalSpacer_44);

        checkBox_HDMI_exposure_auto = new QCheckBox(scrollAreaWidgetContents_3);
        checkBox_HDMI_exposure_auto->setObjectName(QString::fromUtf8("checkBox_HDMI_exposure_auto"));
        sizePolicy3.setHeightForWidth(checkBox_HDMI_exposure_auto->sizePolicy().hasHeightForWidth());
        checkBox_HDMI_exposure_auto->setSizePolicy(sizePolicy3);
        checkBox_HDMI_exposure_auto->setMaximumSize(QSize(100, 30));
        checkBox_HDMI_exposure_auto->setStyleSheet(QString::fromUtf8(""));
        checkBox_HDMI_exposure_auto->setIconSize(QSize(30, 30));
        checkBox_HDMI_exposure_auto->setChecked(true);

        horizontalLayout_45->addWidget(checkBox_HDMI_exposure_auto);

        horizontalSpacer_54 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_45->addItem(horizontalSpacer_54);


        verticalLayout_25->addLayout(horizontalLayout_45);

        horizontalLayout_46 = new QHBoxLayout();
        horizontalLayout_46->setObjectName(QString::fromUtf8("horizontalLayout_46"));
        HDMI_exposure = new QPushButton(scrollAreaWidgetContents_3);
        HDMI_exposure->setObjectName(QString::fromUtf8("HDMI_exposure"));
        HDMI_exposure->setMinimumSize(QSize(220, 50));
        HDMI_exposure->setMaximumSize(QSize(220, 50));
        HDMI_exposure->setFont(font2);
        HDMI_exposure->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        HDMI_exposure->setCheckable(true);

        horizontalLayout_46->addWidget(HDMI_exposure);

        horizontalSpacer_45 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_46->addItem(horizontalSpacer_45);

        spinBox_HDMI_exposure = new QSpinBox(scrollAreaWidgetContents_3);
        spinBox_HDMI_exposure->setObjectName(QString::fromUtf8("spinBox_HDMI_exposure"));
        spinBox_HDMI_exposure->setMaximumSize(QSize(100, 30));
        spinBox_HDMI_exposure->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_46->addWidget(spinBox_HDMI_exposure);

        horizontalSpacer_55 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_46->addItem(horizontalSpacer_55);


        verticalLayout_25->addLayout(horizontalLayout_46);

        horizontalLayout_47 = new QHBoxLayout();
        horizontalLayout_47->setObjectName(QString::fromUtf8("horizontalLayout_47"));
        HDMI_white_auto = new QPushButton(scrollAreaWidgetContents_3);
        HDMI_white_auto->setObjectName(QString::fromUtf8("HDMI_white_auto"));
        HDMI_white_auto->setMinimumSize(QSize(220, 50));
        HDMI_white_auto->setMaximumSize(QSize(220, 50));
        HDMI_white_auto->setFont(font2);
        HDMI_white_auto->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        HDMI_white_auto->setCheckable(true);

        horizontalLayout_47->addWidget(HDMI_white_auto);

        horizontalSpacer_46 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_47->addItem(horizontalSpacer_46);

        checkBox_HDMI_white = new QCheckBox(scrollAreaWidgetContents_3);
        checkBox_HDMI_white->setObjectName(QString::fromUtf8("checkBox_HDMI_white"));
        sizePolicy3.setHeightForWidth(checkBox_HDMI_white->sizePolicy().hasHeightForWidth());
        checkBox_HDMI_white->setSizePolicy(sizePolicy3);
        checkBox_HDMI_white->setMaximumSize(QSize(100, 30));
        checkBox_HDMI_white->setStyleSheet(QString::fromUtf8(""));
        checkBox_HDMI_white->setIconSize(QSize(30, 30));
        checkBox_HDMI_white->setChecked(true);

        horizontalLayout_47->addWidget(checkBox_HDMI_white);

        horizontalSpacer_56 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_47->addItem(horizontalSpacer_56);


        verticalLayout_25->addLayout(horizontalLayout_47);

        horizontalLayout_48 = new QHBoxLayout();
        horizontalLayout_48->setObjectName(QString::fromUtf8("horizontalLayout_48"));
        HDMI_white = new QPushButton(scrollAreaWidgetContents_3);
        HDMI_white->setObjectName(QString::fromUtf8("HDMI_white"));
        HDMI_white->setMinimumSize(QSize(220, 50));
        HDMI_white->setMaximumSize(QSize(220, 50));
        HDMI_white->setFont(font2);
        HDMI_white->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        HDMI_white->setCheckable(true);

        horizontalLayout_48->addWidget(HDMI_white);

        horizontalSpacer_47 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_48->addItem(horizontalSpacer_47);

        spinBox_HDMI_white = new QSpinBox(scrollAreaWidgetContents_3);
        spinBox_HDMI_white->setObjectName(QString::fromUtf8("spinBox_HDMI_white"));
        spinBox_HDMI_white->setMaximumSize(QSize(100, 30));
        spinBox_HDMI_white->setStyleSheet(QString::fromUtf8("QSpinBox {\n"
"    border: 1px solid gray;\n"
"    border-radius: 3px;\n"
"    padding-right: 20px;  /* \344\270\272\345\212\240\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"    padding-left: 20px;   /* \344\270\272\345\207\217\345\217\267\346\214\211\351\222\256\351\242\204\347\225\231\347\251\272\351\227\264 */\n"
"}\n"
"\n"
"/* \345\212\240\345\217\267\346\214\211\351\222\256\357\274\210\345\217\263\344\276\247\357\274\211 */\n"
"QSpinBox::up-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: right center;  /* \345\233\272\345\256\232\345\234\250\345\217\263\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\212\240\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\212\240\345\217\267\345\233\276\346\240\207 */\n"
"}\n"
"\n"
"/* \345\207\217\345\217\267\346\214\211\351\222\256\357\274\210\345\267\246\344\276\247\357\274\211"
                        " */\n"
"QSpinBox::down-button {\n"
"    subcontrol-origin: border;  /* \347\233\270\345\257\271\344\272\216\350\276\271\346\241\206\345\256\232\344\275\215 */\n"
"    subcontrol-position: left center;  /* \345\233\272\345\256\232\345\234\250\345\267\246\344\276\247 */\n"
"    width: 20px;\n"
"    image: url(:/icons/\345\207\217\345\217\267.png);  /* \350\207\252\345\256\232\344\271\211\345\207\217\345\217\267\345\233\276\346\240\207 */\n"
"}"));

        horizontalLayout_48->addWidget(spinBox_HDMI_white);

        horizontalSpacer_57 = new QSpacerItem(200, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_48->addItem(horizontalSpacer_57);


        verticalLayout_25->addLayout(horizontalLayout_48);


        gridLayout_12->addLayout(verticalLayout_25, 0, 0, 1, 1);

        scrollArea_HDMI->setWidget(scrollAreaWidgetContents_3);

        verticalLayout_26->addWidget(scrollArea_HDMI);

        stackedWidget->addWidget(HDMI_imageset);
        page_recordset = new QWidget();
        page_recordset->setObjectName(QString::fromUtf8("page_recordset"));
        verticalLayout_12 = new QVBoxLayout(page_recordset);
        verticalLayout_12->setSpacing(0);
        verticalLayout_12->setObjectName(QString::fromUtf8("verticalLayout_12"));
        verticalLayout_12->setContentsMargins(0, 0, 0, 0);
        verticalLayout_9 = new QVBoxLayout();
        verticalLayout_9->setObjectName(QString::fromUtf8("verticalLayout_9"));
        verticalLayout_10 = new QVBoxLayout();
        verticalLayout_10->setObjectName(QString::fromUtf8("verticalLayout_10"));
        horizontalLayout_18 = new QHBoxLayout();
        horizontalLayout_18->setObjectName(QString::fromUtf8("horizontalLayout_18"));
        horizontalLayout_18->setSizeConstraint(QLayout::SetFixedSize);
        recordset_1 = new QLabel(page_recordset);
        recordset_1->setObjectName(QString::fromUtf8("recordset_1"));
        sizePolicy2.setHeightForWidth(recordset_1->sizePolicy().hasHeightForWidth());
        recordset_1->setSizePolicy(sizePolicy2);
        recordset_1->setMinimumSize(QSize(0, 60));
        recordset_1->setMaximumSize(QSize(16777215, 60));
        recordset_1->setFont(font2);
        recordset_1->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        horizontalLayout_18->addWidget(recordset_1);

        horizontalSpacer_17 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_18->addItem(horizontalSpacer_17);


        verticalLayout_10->addLayout(horizontalLayout_18);

        line_7 = new QFrame(page_recordset);
        line_7->setObjectName(QString::fromUtf8("line_7"));
        line_7->setMinimumSize(QSize(0, 0));
        line_7->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line_7->setMidLineWidth(5);
        line_7->setFrameShape(QFrame::HLine);
        line_7->setFrameShadow(QFrame::Sunken);

        verticalLayout_10->addWidget(line_7);

        horizontalLayout_19 = new QHBoxLayout();
        horizontalLayout_19->setObjectName(QString::fromUtf8("horizontalLayout_19"));
        verticalLayout_11 = new QVBoxLayout();
        verticalLayout_11->setObjectName(QString::fromUtf8("verticalLayout_11"));
        record_UVC = new QPushButton(page_recordset);
        record_UVC->setObjectName(QString::fromUtf8("record_UVC"));
        record_UVC->setMinimumSize(QSize(0, 50));
        record_UVC->setFont(font2);
        record_UVC->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        record_UVC->setCheckable(true);
        record_UVC->setAutoRepeat(false);
        record_UVC->setAutoExclusive(false);

        verticalLayout_11->addWidget(record_UVC);

        record_HDMI = new QPushButton(page_recordset);
        record_HDMI->setObjectName(QString::fromUtf8("record_HDMI"));
        record_HDMI->setMinimumSize(QSize(0, 50));
        record_HDMI->setFont(font2);
        record_HDMI->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        record_HDMI->setCheckable(true);

        verticalLayout_11->addWidget(record_HDMI);


        horizontalLayout_19->addLayout(verticalLayout_11);

        horizontalSpacer_18 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_19->addItem(horizontalSpacer_18);


        verticalLayout_10->addLayout(horizontalLayout_19);


        verticalLayout_9->addLayout(verticalLayout_10);

        verticalSpacer_4 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_9->addItem(verticalSpacer_4);


        verticalLayout_12->addLayout(verticalLayout_9);

        stackedWidget->addWidget(page_recordset);
        page_recordset_2 = new QWidget();
        page_recordset_2->setObjectName(QString::fromUtf8("page_recordset_2"));
        verticalLayout_15 = new QVBoxLayout(page_recordset_2);
        verticalLayout_15->setObjectName(QString::fromUtf8("verticalLayout_15"));
        verticalLayout_15->setContentsMargins(0, 0, 0, 0);
        horizontalLayout_20 = new QHBoxLayout();
        horizontalLayout_20->setObjectName(QString::fromUtf8("horizontalLayout_20"));
        horizontalLayout_20->setSizeConstraint(QLayout::SetFixedSize);
        recordset_2 = new QLabel(page_recordset_2);
        recordset_2->setObjectName(QString::fromUtf8("recordset_2"));
        sizePolicy2.setHeightForWidth(recordset_2->sizePolicy().hasHeightForWidth());
        recordset_2->setSizePolicy(sizePolicy2);
        recordset_2->setMinimumSize(QSize(0, 60));
        recordset_2->setMaximumSize(QSize(16777215, 60));
        recordset_2->setFont(font2);
        recordset_2->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        horizontalLayout_20->addWidget(recordset_2);

        horizontalSpacer_19 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_20->addItem(horizontalSpacer_19);


        verticalLayout_15->addLayout(horizontalLayout_20);

        line_8 = new QFrame(page_recordset_2);
        line_8->setObjectName(QString::fromUtf8("line_8"));
        line_8->setMinimumSize(QSize(0, 0));
        line_8->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line_8->setMidLineWidth(5);
        line_8->setFrameShape(QFrame::HLine);
        line_8->setFrameShadow(QFrame::Sunken);

        verticalLayout_15->addWidget(line_8);

        scrollArea = new QScrollArea(page_recordset_2);
        scrollArea->setObjectName(QString::fromUtf8("scrollArea"));
        scrollArea->setWidgetResizable(true);
        scrollAreaWidgetContents = new QWidget();
        scrollAreaWidgetContents->setObjectName(QString::fromUtf8("scrollAreaWidgetContents"));
        scrollAreaWidgetContents->setGeometry(QRect(0, 0, 867, 460));
        verticalLayout_14 = new QVBoxLayout(scrollAreaWidgetContents);
        verticalLayout_14->setObjectName(QString::fromUtf8("verticalLayout_14"));
        verticalLayout_14->setContentsMargins(0, 0, 0, 0);
        horizontalLayout_21 = new QHBoxLayout();
        horizontalLayout_21->setObjectName(QString::fromUtf8("horizontalLayout_21"));
        record_fbl = new QPushButton(scrollAreaWidgetContents);
        record_fbl->setObjectName(QString::fromUtf8("record_fbl"));
        record_fbl->setMinimumSize(QSize(220, 50));
        record_fbl->setMaximumSize(QSize(220, 50));
        record_fbl->setFont(font2);
        record_fbl->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        record_fbl->setCheckable(true);

        horizontalLayout_21->addWidget(record_fbl);

        horizontalSpacer_20 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_21->addItem(horizontalSpacer_20);

        label_choose_fbl = new QLabel(scrollAreaWidgetContents);
        label_choose_fbl->setObjectName(QString::fromUtf8("label_choose_fbl"));
        label_choose_fbl->setMinimumSize(QSize(0, 50));
        label_choose_fbl->setFont(font2);
        label_choose_fbl->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);\n"
"border:none;"));
        label_choose_fbl->setAlignment(Qt::AlignCenter);

        horizontalLayout_21->addWidget(label_choose_fbl);


        verticalLayout_14->addLayout(horizontalLayout_21);

        horizontalLayout_22 = new QHBoxLayout();
        horizontalLayout_22->setObjectName(QString::fromUtf8("horizontalLayout_22"));
        record_fps = new QPushButton(scrollAreaWidgetContents);
        record_fps->setObjectName(QString::fromUtf8("record_fps"));
        record_fps->setMinimumSize(QSize(220, 50));
        record_fps->setMaximumSize(QSize(220, 50));
        record_fps->setFont(font2);
        record_fps->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        record_fps->setCheckable(true);

        horizontalLayout_22->addWidget(record_fps);

        horizontalSpacer_21 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_22->addItem(horizontalSpacer_21);

        label_choose_fps = new QLabel(scrollAreaWidgetContents);
        label_choose_fps->setObjectName(QString::fromUtf8("label_choose_fps"));
        label_choose_fps->setMinimumSize(QSize(0, 50));
        label_choose_fps->setFont(font2);
        label_choose_fps->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);\n"
"border:none;"));
        label_choose_fps->setAlignment(Qt::AlignCenter);

        horizontalLayout_22->addWidget(label_choose_fps);


        verticalLayout_14->addLayout(horizontalLayout_22);

        horizontalLayout_23 = new QHBoxLayout();
        horizontalLayout_23->setObjectName(QString::fromUtf8("horizontalLayout_23"));
        record_encode = new QPushButton(scrollAreaWidgetContents);
        record_encode->setObjectName(QString::fromUtf8("record_encode"));
        record_encode->setMinimumSize(QSize(220, 50));
        record_encode->setMaximumSize(QSize(220, 50));
        record_encode->setFont(font2);
        record_encode->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        record_encode->setCheckable(true);

        horizontalLayout_23->addWidget(record_encode);

        horizontalSpacer_22 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_23->addItem(horizontalSpacer_22);

        label_choose_encode = new QLabel(scrollAreaWidgetContents);
        label_choose_encode->setObjectName(QString::fromUtf8("label_choose_encode"));
        label_choose_encode->setMinimumSize(QSize(0, 50));
        label_choose_encode->setFont(font2);
        label_choose_encode->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);\n"
"border:none;"));
        label_choose_encode->setAlignment(Qt::AlignCenter);

        horizontalLayout_23->addWidget(label_choose_encode);


        verticalLayout_14->addLayout(horizontalLayout_23);

        horizontalLayout_24 = new QHBoxLayout();
        horizontalLayout_24->setObjectName(QString::fromUtf8("horizontalLayout_24"));
        record_quality = new QPushButton(scrollAreaWidgetContents);
        record_quality->setObjectName(QString::fromUtf8("record_quality"));
        record_quality->setMinimumSize(QSize(220, 50));
        record_quality->setMaximumSize(QSize(220, 50));
        record_quality->setFont(font2);
        record_quality->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        record_quality->setCheckable(true);

        horizontalLayout_24->addWidget(record_quality);

        horizontalSpacer_23 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_24->addItem(horizontalSpacer_23);

        label_choose_quality = new QLabel(scrollAreaWidgetContents);
        label_choose_quality->setObjectName(QString::fromUtf8("label_choose_quality"));
        label_choose_quality->setMinimumSize(QSize(0, 50));
        label_choose_quality->setFont(font2);
        label_choose_quality->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);\n"
"border:none;"));
        label_choose_quality->setAlignment(Qt::AlignCenter);

        horizontalLayout_24->addWidget(label_choose_quality);


        verticalLayout_14->addLayout(horizontalLayout_24);

        horizontalLayout_25 = new QHBoxLayout();
        horizontalLayout_25->setObjectName(QString::fromUtf8("horizontalLayout_25"));
        record_ml = new QPushButton(scrollAreaWidgetContents);
        record_ml->setObjectName(QString::fromUtf8("record_ml"));
        record_ml->setMinimumSize(QSize(220, 50));
        record_ml->setMaximumSize(QSize(220, 50));
        record_ml->setFont(font2);
        record_ml->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        record_ml->setCheckable(true);

        horizontalLayout_25->addWidget(record_ml);

        horizontalSpacer_24 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_25->addItem(horizontalSpacer_24);


        verticalLayout_14->addLayout(horizontalLayout_25);

        horizontalLayout_26 = new QHBoxLayout();
        horizontalLayout_26->setObjectName(QString::fromUtf8("horizontalLayout_26"));
        record_storage = new QPushButton(scrollAreaWidgetContents);
        record_storage->setObjectName(QString::fromUtf8("record_storage"));
        record_storage->setMinimumSize(QSize(220, 50));
        record_storage->setMaximumSize(QSize(220, 50));
        record_storage->setFont(font2);
        record_storage->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        record_storage->setCheckable(true);

        horizontalLayout_26->addWidget(record_storage);

        horizontalSpacer_25 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_26->addItem(horizontalSpacer_25);

        laebl_choose_storage = new QLabel(scrollAreaWidgetContents);
        laebl_choose_storage->setObjectName(QString::fromUtf8("laebl_choose_storage"));
        laebl_choose_storage->setMinimumSize(QSize(0, 50));
        laebl_choose_storage->setFont(font2);
        laebl_choose_storage->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);\n"
"border:none;"));
        laebl_choose_storage->setAlignment(Qt::AlignCenter);

        horizontalLayout_26->addWidget(laebl_choose_storage);


        verticalLayout_14->addLayout(horizontalLayout_26);

        verticalLayout_13 = new QVBoxLayout();
        verticalLayout_13->setObjectName(QString::fromUtf8("verticalLayout_13"));
        horizontalLayout_27 = new QHBoxLayout();
        horizontalLayout_27->setObjectName(QString::fromUtf8("horizontalLayout_27"));
        start_recordingaudioset = new QPushButton(scrollAreaWidgetContents);
        start_recordingaudioset->setObjectName(QString::fromUtf8("start_recordingaudioset"));
        start_recordingaudioset->setMinimumSize(QSize(220, 50));
        start_recordingaudioset->setMaximumSize(QSize(220, 50));
        start_recordingaudioset->setFont(font2);
        start_recordingaudioset->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        start_recordingaudioset->setCheckable(true);

        horizontalLayout_27->addWidget(start_recordingaudioset);

        horizontalSpacer_26 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_27->addItem(horizontalSpacer_26);

        laebl_choose_audio = new QLabel(scrollAreaWidgetContents);
        laebl_choose_audio->setObjectName(QString::fromUtf8("laebl_choose_audio"));
        laebl_choose_audio->setMinimumSize(QSize(0, 50));
        laebl_choose_audio->setFont(font2);
        laebl_choose_audio->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);\n"
"border:none;"));
        laebl_choose_audio->setAlignment(Qt::AlignCenter);

        horizontalLayout_27->addWidget(laebl_choose_audio);


        verticalLayout_13->addLayout(horizontalLayout_27);

        horizontalLayout_28 = new QHBoxLayout();
        horizontalLayout_28->setObjectName(QString::fromUtf8("horizontalLayout_28"));
        start_recordingaudioopen = new QPushButton(scrollAreaWidgetContents);
        start_recordingaudioopen->setObjectName(QString::fromUtf8("start_recordingaudioopen"));
        start_recordingaudioopen->setMinimumSize(QSize(220, 50));
        start_recordingaudioopen->setMaximumSize(QSize(220, 50));
        start_recordingaudioopen->setFont(font2);
        start_recordingaudioopen->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"background-color: rgba(255, 255, 255,127);\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        start_recordingaudioopen->setCheckable(true);

        horizontalLayout_28->addWidget(start_recordingaudioopen);

        horizontalSpacer_27 = new QSpacerItem(40, 20, QSizePolicy::Fixed, QSizePolicy::Minimum);

        horizontalLayout_28->addItem(horizontalSpacer_27);

        laebl_audioopen = new QLabel(scrollAreaWidgetContents);
        laebl_audioopen->setObjectName(QString::fromUtf8("laebl_audioopen"));
        laebl_audioopen->setMinimumSize(QSize(0, 50));
        laebl_audioopen->setFont(font2);
        laebl_audioopen->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);\n"
"border:none;"));
        laebl_audioopen->setAlignment(Qt::AlignCenter);

        horizontalLayout_28->addWidget(laebl_audioopen);


        verticalLayout_13->addLayout(horizontalLayout_28);


        verticalLayout_14->addLayout(verticalLayout_13);

        scrollArea->setWidget(scrollAreaWidgetContents);

        verticalLayout_15->addWidget(scrollArea);

        stackedWidget->addWidget(page_recordset_2);
        page_filemanage = new QWidget();
        page_filemanage->setObjectName(QString::fromUtf8("page_filemanage"));
        verticalLayout_19 = new QVBoxLayout(page_filemanage);
        verticalLayout_19->setObjectName(QString::fromUtf8("verticalLayout_19"));
        verticalLayout_19->setContentsMargins(0, 0, 0, 0);
        verticalLayout_16 = new QVBoxLayout();
        verticalLayout_16->setObjectName(QString::fromUtf8("verticalLayout_16"));
        verticalLayout_17 = new QVBoxLayout();
        verticalLayout_17->setObjectName(QString::fromUtf8("verticalLayout_17"));
        horizontalLayout_29 = new QHBoxLayout();
        horizontalLayout_29->setObjectName(QString::fromUtf8("horizontalLayout_29"));
        horizontalLayout_29->setSizeConstraint(QLayout::SetFixedSize);
        filemanage = new QLabel(page_filemanage);
        filemanage->setObjectName(QString::fromUtf8("filemanage"));
        sizePolicy2.setHeightForWidth(filemanage->sizePolicy().hasHeightForWidth());
        filemanage->setSizePolicy(sizePolicy2);
        filemanage->setMinimumSize(QSize(0, 60));
        filemanage->setMaximumSize(QSize(16777215, 60));
        filemanage->setFont(font2);
        filemanage->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        horizontalLayout_29->addWidget(filemanage);

        horizontalSpacer_28 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_29->addItem(horizontalSpacer_28);


        verticalLayout_17->addLayout(horizontalLayout_29);

        line_9 = new QFrame(page_filemanage);
        line_9->setObjectName(QString::fromUtf8("line_9"));
        line_9->setMinimumSize(QSize(0, 0));
        line_9->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line_9->setMidLineWidth(5);
        line_9->setFrameShape(QFrame::HLine);
        line_9->setFrameShadow(QFrame::Sunken);

        verticalLayout_17->addWidget(line_9);

        horizontalLayout_30 = new QHBoxLayout();
        horizontalLayout_30->setObjectName(QString::fromUtf8("horizontalLayout_30"));
        verticalLayout_18 = new QVBoxLayout();
        verticalLayout_18->setObjectName(QString::fromUtf8("verticalLayout_18"));
        file_picture = new QPushButton(page_filemanage);
        file_picture->setObjectName(QString::fromUtf8("file_picture"));
        file_picture->setMinimumSize(QSize(0, 50));
        file_picture->setFont(font2);
        file_picture->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        file_picture->setCheckable(true);
        file_picture->setAutoRepeat(false);
        file_picture->setAutoExclusive(false);

        verticalLayout_18->addWidget(file_picture);

        file_video = new QPushButton(page_filemanage);
        file_video->setObjectName(QString::fromUtf8("file_video"));
        file_video->setMinimumSize(QSize(0, 50));
        file_video->setFont(font2);
        file_video->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        file_video->setCheckable(true);

        verticalLayout_18->addWidget(file_video);


        horizontalLayout_30->addLayout(verticalLayout_18);

        horizontalSpacer_29 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_30->addItem(horizontalSpacer_29);


        verticalLayout_17->addLayout(horizontalLayout_30);


        verticalLayout_16->addLayout(verticalLayout_17);

        verticalSpacer_5 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_16->addItem(verticalSpacer_5);


        verticalLayout_19->addLayout(verticalLayout_16);

        stackedWidget->addWidget(page_filemanage);
        viewpictures = new QWidget();
        viewpictures->setObjectName(QString::fromUtf8("viewpictures"));
        gridLayout_9 = new QGridLayout(viewpictures);
        gridLayout_9->setSpacing(0);
        gridLayout_9->setObjectName(QString::fromUtf8("gridLayout_9"));
        gridLayout_9->setContentsMargins(0, 0, 0, 0);
        listView_viewpictures = new QListView(viewpictures);
        listView_viewpictures->setObjectName(QString::fromUtf8("listView_viewpictures"));
        listView_viewpictures->setStyleSheet(QString::fromUtf8("background-color: rgba(119, 118, 123, 127);\n"
""));

        gridLayout_9->addWidget(listView_viewpictures, 0, 0, 1, 1);

        stackedWidget->addWidget(viewpictures);
        videofile = new QWidget();
        videofile->setObjectName(QString::fromUtf8("videofile"));
        gridLayout_8 = new QGridLayout(videofile);
        gridLayout_8->setSpacing(0);
        gridLayout_8->setObjectName(QString::fromUtf8("gridLayout_8"));
        gridLayout_8->setContentsMargins(0, 0, 0, 0);
        listView_videofile = new QListView(videofile);
        listView_videofile->setObjectName(QString::fromUtf8("listView_videofile"));
        listView_videofile->setStyleSheet(QString::fromUtf8("background-color: rgba(119, 118, 123, 127);"));

        gridLayout_8->addWidget(listView_videofile, 0, 0, 1, 1);

        stackedWidget->addWidget(videofile);
        page_systemset = new QWidget();
        page_systemset->setObjectName(QString::fromUtf8("page_systemset"));
        page_systemset->setStyleSheet(QString::fromUtf8("background-color: rgba(119, 118, 123,127);"));
        layoutWidget_11 = new QWidget(page_systemset);
        layoutWidget_11->setObjectName(QString::fromUtf8("layoutWidget_11"));
        layoutWidget_11->setGeometry(QRect(0, 0, 883, 422));
        verticalLayout_20 = new QVBoxLayout(layoutWidget_11);
        verticalLayout_20->setObjectName(QString::fromUtf8("verticalLayout_20"));
        verticalLayout_20->setContentsMargins(0, 0, 0, 0);
        verticalLayout_21 = new QVBoxLayout();
        verticalLayout_21->setObjectName(QString::fromUtf8("verticalLayout_21"));
        horizontalLayout_31 = new QHBoxLayout();
        horizontalLayout_31->setObjectName(QString::fromUtf8("horizontalLayout_31"));
        horizontalLayout_31->setSizeConstraint(QLayout::SetFixedSize);
        systemset = new QLabel(layoutWidget_11);
        systemset->setObjectName(QString::fromUtf8("systemset"));
        sizePolicy2.setHeightForWidth(systemset->sizePolicy().hasHeightForWidth());
        systemset->setSizePolicy(sizePolicy2);
        systemset->setMinimumSize(QSize(0, 60));
        systemset->setMaximumSize(QSize(16777215, 60));
        systemset->setFont(font2);
        systemset->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        horizontalLayout_31->addWidget(systemset);

        horizontalSpacer_30 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_31->addItem(horizontalSpacer_30);


        verticalLayout_21->addLayout(horizontalLayout_31);

        line_10 = new QFrame(layoutWidget_11);
        line_10->setObjectName(QString::fromUtf8("line_10"));
        line_10->setMinimumSize(QSize(0, 0));
        line_10->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line_10->setMidLineWidth(5);
        line_10->setFrameShape(QFrame::HLine);
        line_10->setFrameShadow(QFrame::Sunken);

        verticalLayout_21->addWidget(line_10);

        horizontalLayout_32 = new QHBoxLayout();
        horizontalLayout_32->setObjectName(QString::fromUtf8("horizontalLayout_32"));
        verticalLayout_22 = new QVBoxLayout();
        verticalLayout_22->setObjectName(QString::fromUtf8("verticalLayout_22"));
        camera_4 = new QPushButton(layoutWidget_11);
        camera_4->setObjectName(QString::fromUtf8("camera_4"));
        camera_4->setMinimumSize(QSize(0, 50));
        camera_4->setFont(font2);
        camera_4->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        camera_4->setCheckable(true);
        camera_4->setAutoRepeat(false);
        camera_4->setAutoExclusive(false);

        verticalLayout_22->addWidget(camera_4);

        custom_4 = new QPushButton(layoutWidget_11);
        custom_4->setObjectName(QString::fromUtf8("custom_4"));
        custom_4->setMinimumSize(QSize(0, 50));
        custom_4->setFont(font2);
        custom_4->setStyleSheet(QString::fromUtf8("QPushButton::pressed,QPushButton::checked{\n"
"	color: rgb(0, 0, 0);\n"
"	\n"
"	background-color: rgba(255, 255, 255,127);\n"
"\n"
"text-align: left;\n"
"border:none;\n"
"}\n"
"QPushButton{border-radius:1px;\n"
"	 border: none;\n"
"\n"
"text-align: left;\n"
"	color: rgb(255, 255, 255);\n"
"	background-color: rgba(90, 90, 90,0);\n"
"\n"
"}"));
        custom_4->setCheckable(true);

        verticalLayout_22->addWidget(custom_4);


        horizontalLayout_32->addLayout(verticalLayout_22);

        horizontalSpacer_31 = new QSpacerItem(40, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout_32->addItem(horizontalSpacer_31);


        verticalLayout_21->addLayout(horizontalLayout_32);


        verticalLayout_20->addLayout(verticalLayout_21);

        verticalSpacer_6 = new QSpacerItem(20, 40, QSizePolicy::Minimum, QSizePolicy::Expanding);

        verticalLayout_20->addItem(verticalSpacer_6);

        stackedWidget->addWidget(page_systemset);

        verticalLayout_3->addWidget(stackedWidget);

        help_menu = new QWidget(centralwidget);
        help_menu->setObjectName(QString::fromUtf8("help_menu"));
        help_menu->setStyleSheet(QString::fromUtf8("background-color: rgba(119, 118, 123,127);"));
        gridLayout_2 = new QGridLayout(help_menu);
        gridLayout_2->setSpacing(0);
        gridLayout_2->setObjectName(QString::fromUtf8("gridLayout_2"));
        gridLayout_2->setContentsMargins(0, 0, 0, 0);
        verticalLayout = new QVBoxLayout();
        verticalLayout->setObjectName(QString::fromUtf8("verticalLayout"));
        line_2 = new QFrame(help_menu);
        line_2->setObjectName(QString::fromUtf8("line_2"));
        line_2->setMinimumSize(QSize(0, 0));
        line_2->setStyleSheet(QString::fromUtf8("color: rgb(119, 118, 123);"));
        line_2->setMidLineWidth(5);
        line_2->setFrameShape(QFrame::HLine);
        line_2->setFrameShadow(QFrame::Sunken);

        verticalLayout->addWidget(line_2);

        horizontalLayout = new QHBoxLayout();
        horizontalLayout->setObjectName(QString::fromUtf8("horizontalLayout"));
        gridLayout = new QGridLayout();
        gridLayout->setObjectName(QString::fromUtf8("gridLayout"));
        Return = new QLabel(help_menu);
        Return->setObjectName(QString::fromUtf8("Return"));
        Return->setMinimumSize(QSize(0, 50));
        QFont font4;
        font4.setPointSize(20);
        Return->setFont(font4);
        Return->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        gridLayout->addWidget(Return, 1, 1, 1, 1);

        select = new QLabel(help_menu);
        select->setObjectName(QString::fromUtf8("select"));
        select->setMinimumSize(QSize(0, 50));
        select->setFont(font4);
        select->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        gridLayout->addWidget(select, 0, 0, 1, 1);

        reset = new QLabel(help_menu);
        reset->setObjectName(QString::fromUtf8("reset"));
        reset->setMinimumSize(QSize(0, 50));
        reset->setFont(font4);
        reset->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        gridLayout->addWidget(reset, 1, 0, 1, 1);

        modification = new QLabel(help_menu);
        modification->setObjectName(QString::fromUtf8("modification"));
        modification->setMinimumSize(QSize(0, 50));
        modification->setFont(font4);
        modification->setStyleSheet(QString::fromUtf8("color: rgb(255, 255, 255);\n"
"background-color: rgba(119, 118, 123,0);"));

        gridLayout->addWidget(modification, 0, 1, 1, 1);


        horizontalLayout->addLayout(gridLayout);

        horizontalSpacer_2 = new QSpacerItem(582, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        horizontalLayout->addItem(horizontalSpacer_2);


        verticalLayout->addLayout(horizontalLayout);


        gridLayout_2->addLayout(verticalLayout, 0, 0, 1, 1);


        verticalLayout_3->addWidget(help_menu);


        gridLayout_4->addLayout(verticalLayout_3, 2, 1, 2, 1);

        horizontalSpacer_4 = new QSpacerItem(319, 20, QSizePolicy::Expanding, QSizePolicy::Minimum);

        gridLayout_4->addItem(horizontalSpacer_4, 3, 2, 1, 1);

        verticalSpacer_2 = new QSpacerItem(20, 59, QSizePolicy::Minimum, QSizePolicy::Expanding);

        gridLayout_4->addItem(verticalSpacer_2, 4, 1, 1, 1);

        MainWindow->setCentralWidget(centralwidget);

        retranslateUi(MainWindow);

        stackedWidget->setCurrentIndex(8);


        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "MainWindow", nullptr));
        label_channel->setText(QCoreApplication::translate("MainWindow", "\351\200\232\351\201\223\357\274\232UVC", nullptr));
        label_fbl->setText(QCoreApplication::translate("MainWindow", "\345\210\206\350\276\250\347\216\207\357\274\2324K", nullptr));
        label_fps->setText(QCoreApplication::translate("MainWindow", "\345\270\247\347\216\207\357\274\23260", nullptr));
        label_BM->setText(QCoreApplication::translate("MainWindow", "\347\274\226\347\240\201\357\274\232H264", nullptr));
        label_ML->setText(QCoreApplication::translate("MainWindow", "\347\240\201\346\265\201\357\274\23240MB", nullptr));
        label_storage->setText(QCoreApplication::translate("MainWindow", "\345\255\230\345\202\250\344\275\215\347\275\256\357\274\232TF\345\215\241", nullptr));
        recordingDotLabel->setText(QString());
        recordingTimeLabel->setText(QCoreApplication::translate("MainWindow", "00:00:00", nullptr));
        main_menu->setText(QCoreApplication::translate("MainWindow", "\344\270\273\350\217\234\345\215\225", nullptr));
        Camera->setText(QCoreApplication::translate("MainWindow", "\345\233\276\345\203\217\345\217\202\346\225\260", nullptr));
        Recordset->setText(QCoreApplication::translate("MainWindow", "\345\275\225\345\203\217\350\256\276\347\275\256", nullptr));
        Filemanage->setText(QCoreApplication::translate("MainWindow", "\346\226\207\344\273\266\347\256\241\347\220\206", nullptr));
        Systemset->setText(QCoreApplication::translate("MainWindow", "\347\263\273\347\273\237\350\256\276\347\275\256", nullptr));
        camera->setText(QCoreApplication::translate("MainWindow", "\345\233\276\345\203\217\345\217\202\346\225\260", nullptr));
        Camera_UVC->setText(QCoreApplication::translate("MainWindow", "UVC", nullptr));
        Camera_HDMI->setText(QCoreApplication::translate("MainWindow", "HDMI", nullptr));
        camera_UVC->setText(QCoreApplication::translate("MainWindow", "UVC\345\233\276\345\203\217\345\217\202\346\225\260", nullptr));
        UVC_brightness->setText(QCoreApplication::translate("MainWindow", "\344\272\256\345\272\246", nullptr));
        UVC_contrast->setText(QCoreApplication::translate("MainWindow", "\345\257\271\346\257\224\345\272\246", nullptr));
        UVC_saturation->setText(QCoreApplication::translate("MainWindow", "\351\245\261\345\222\214\345\272\246", nullptr));
        UVC_hue->setText(QCoreApplication::translate("MainWindow", "\350\211\262\350\260\203", nullptr));
        UVC_exposure_auto->setText(QCoreApplication::translate("MainWindow", "\350\207\252\345\212\250\346\233\235\345\205\211", nullptr));
        checkBox_UVC_exposure->setText(QCoreApplication::translate("MainWindow", "\346\230\257\345\220\246\350\207\252\345\212\250", nullptr));
        UVC_exposure->setText(QCoreApplication::translate("MainWindow", "\346\233\235\345\205\211", nullptr));
        UVC_white_auto->setText(QCoreApplication::translate("MainWindow", "\350\207\252\345\212\250\347\231\275\345\271\263\350\241\241", nullptr));
        checkBox_UVC_white->setText(QCoreApplication::translate("MainWindow", "\346\230\257\345\220\246\350\207\252\345\212\250", nullptr));
        UVC_white->setText(QCoreApplication::translate("MainWindow", "\347\231\275\345\271\263\350\241\241", nullptr));
        camera_HDMI->setText(QCoreApplication::translate("MainWindow", "HDMI\345\233\276\345\203\217\345\217\202\346\225\260", nullptr));
        HDMI_brightness->setText(QCoreApplication::translate("MainWindow", "\344\272\256\345\272\246", nullptr));
        HDMI_contrast->setText(QCoreApplication::translate("MainWindow", "\345\257\271\346\257\224\345\272\246", nullptr));
        HDMI_saturation->setText(QCoreApplication::translate("MainWindow", "\351\245\261\345\222\214\345\272\246", nullptr));
        HDMI_hue->setText(QCoreApplication::translate("MainWindow", "\350\211\262\350\260\203", nullptr));
        HDMI_exposure_auto->setText(QCoreApplication::translate("MainWindow", "\350\207\252\345\212\250\346\233\235\345\205\211", nullptr));
        checkBox_HDMI_exposure_auto->setText(QCoreApplication::translate("MainWindow", "\346\230\257\345\220\246\350\207\252\345\212\250", nullptr));
        HDMI_exposure->setText(QCoreApplication::translate("MainWindow", "\346\233\235\345\205\211", nullptr));
        HDMI_white_auto->setText(QCoreApplication::translate("MainWindow", "\350\207\252\345\212\250\347\231\275\345\271\263\350\241\241", nullptr));
        checkBox_HDMI_white->setText(QCoreApplication::translate("MainWindow", "\346\230\257\345\220\246\350\207\252\345\212\250", nullptr));
        HDMI_white->setText(QCoreApplication::translate("MainWindow", "\347\231\275\345\271\263\350\241\241", nullptr));
        recordset_1->setText(QCoreApplication::translate("MainWindow", "\345\275\225\345\203\217\350\256\276\347\275\256", nullptr));
        record_UVC->setText(QCoreApplication::translate("MainWindow", "UVC", nullptr));
        record_HDMI->setText(QCoreApplication::translate("MainWindow", "HDMI", nullptr));
        recordset_2->setText(QCoreApplication::translate("MainWindow", "\345\275\225\345\203\217\350\256\276\347\275\256", nullptr));
        record_fbl->setText(QCoreApplication::translate("MainWindow", "\345\210\206\350\276\250\347\216\207", nullptr));
        label_choose_fbl->setText(QCoreApplication::translate("MainWindow", "< 4K >", nullptr));
        record_fps->setText(QCoreApplication::translate("MainWindow", "\345\270\247\347\216\207", nullptr));
        label_choose_fps->setText(QCoreApplication::translate("MainWindow", "< 60 >", nullptr));
        record_encode->setText(QCoreApplication::translate("MainWindow", "\347\274\226\347\240\201\346\226\271\345\274\217", nullptr));
        label_choose_encode->setText(QCoreApplication::translate("MainWindow", "< H264 >", nullptr));
        record_quality->setText(QCoreApplication::translate("MainWindow", "\350\264\250\351\207\217", nullptr));
        label_choose_quality->setText(QCoreApplication::translate("MainWindow", "< \344\270\255 >", nullptr));
        record_ml->setText(QCoreApplication::translate("MainWindow", "\350\207\252\345\256\232\344\271\211\347\240\201\346\265\201", nullptr));
        record_storage->setText(QCoreApplication::translate("MainWindow", "\345\255\230\345\202\250\344\275\215\347\275\256", nullptr));
        laebl_choose_storage->setText(QCoreApplication::translate("MainWindow", "< TF\345\215\241>", nullptr));
        start_recordingaudioset->setText(QCoreApplication::translate("MainWindow", "\345\275\225\351\237\263\350\256\276\347\275\256", nullptr));
        laebl_choose_audio->setText(QCoreApplication::translate("MainWindow", "<\351\272\246\345\205\213\351\243\216>", nullptr));
        start_recordingaudioopen->setText(QCoreApplication::translate("MainWindow", "\345\275\225\351\237\263\345\274\200\345\205\263", nullptr));
        laebl_audioopen->setText(QCoreApplication::translate("MainWindow", "<\345\205\263>", nullptr));
        filemanage->setText(QCoreApplication::translate("MainWindow", "\346\226\207\344\273\266\347\256\241\347\220\206", nullptr));
        file_picture->setText(QCoreApplication::translate("MainWindow", "\345\233\276\345\203\217\346\226\207\344\273\266", nullptr));
        file_video->setText(QCoreApplication::translate("MainWindow", "\350\247\206\351\242\221\346\226\207\344\273\266", nullptr));
        systemset->setText(QCoreApplication::translate("MainWindow", "\347\263\273\347\273\237\350\256\276\347\275\256", nullptr));
        camera_4->setText(QCoreApplication::translate("MainWindow", "UVC", nullptr));
        custom_4->setText(QCoreApplication::translate("MainWindow", "HDMI", nullptr));
        Return->setText(QCoreApplication::translate("MainWindow", "[Menu]\350\277\224\345\233\236", nullptr));
        select->setText(QCoreApplication::translate("MainWindow", "[\342\254\206 \342\254\207]\351\200\211\346\213\251", nullptr));
        reset->setText(QCoreApplication::translate("MainWindow", "[Reset]\347\241\256\345\256\232", nullptr));
        modification->setText(QCoreApplication::translate("MainWindow", "[\342\254\205 \342\236\241]\344\277\256\346\224\271", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
