#ifndef CUSTOMDIALOG_H
#define CUSTOMDIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QPushButton>
#include <QProgressBar>
#include <QMouseEvent>
#include <QPaintEvent>
#include <QPainter>
#include <QStyleOption>

class CustomDialog : public QDialog
{
    Q_OBJECT

public:
    explicit CustomDialog(QWidget *parent = nullptr);
    ~CustomDialog();

    // 设置对话框标题
    void setDialogTitle(const QString &title);
    
    // 设置对话框内容
    void setDialogContent(QWidget *content);
    
    // 设置固定大小
    void setDialogSize(int width, int height);

    // 隐藏关闭按钮
    void hideCloseButton();

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;

private slots:
    void onCloseButtonClicked();

private:
    void setupUI();
    
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_titleLayout;
    QLabel *m_titleLabel;
    QPushButton *m_closeButton;
    QWidget *m_contentWidget;
    
    // 用于拖拽窗口
    QPoint m_dragPosition;
    bool m_dragging;
};

#endif // CUSTOMDIALOG_H
