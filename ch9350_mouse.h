#ifndef CH935__MOUSE_H
#define CH935__MOUSE_H

#include <QThread>

class ch9350_mouse : public QThread
{
public:
    explicit ch9350_mouse(const QString &port = "/dev/ttyS10",
                         QObject *parent = nullptr);
    ~ch9350_mouse();
protected:
    void run() override;

private:
    QString m_port="/dev/ttyS10";
    bool m_stop = false;
    int m_uinput=-1;
    QString m_tty;
};

#endif // CH935__MOUSE_H
