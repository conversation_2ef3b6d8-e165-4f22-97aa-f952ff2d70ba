#include "ch9350_mouse.h"
#include "uart.h"
#include <QDebug>
#include <QCursor>
#include <linux/uinput.h>
#include <unistd.h>
#include <fcntl.h>
#include <string.h>
static int openUinput()
{
    int fd = open("/dev/uinput", O_WRONLY | O_NONBLOCK);
    if (fd < 0) {
        qWarning() << "uinput open failed";
        return -1;
    }

    /* 声明事件类型 */
    ioctl(fd, UI_SET_EVBIT, EV_REL);
    ioctl(fd, UI_SET_RELBIT, REL_X);
    ioctl(fd, UI_SET_RELBIT, REL_Y);
    ioctl(fd, UI_SET_RELBIT, REL_WHEEL);

    ioctl(fd, UI_SET_EVBIT, EV_KEY);
    ioctl(fd, UI_SET_KEYBIT, BTN_LEFT);
    ioctl(fd, UI_SET_KEYBIT, BTN_RIGHT);
    ioctl(fd, UI_SET_KEYBIT, BTN_MIDDLE);

    struct uinput_setup usetup{};
    strncpy(usetup.name, "CH9350_virtual_mouse", UINPUT_MAX_NAME_SIZE);
    usetup.id.bustype = BUS_USB;
    usetup.id.vendor  = 0x1234;
    usetup.id.product = 0x5678;
    usetup.id.version = 1;

    ioctl(fd, UI_DEV_SETUP, &usetup);
    ioctl(fd, UI_DEV_CREATE);
    qDebug() << "uinput virtual mouse created";
    return fd;
}

static void emitEvent(int fd, __u16 type, __u16 code, __s32 value)
{
    struct input_event ev{};
    ev.type  = type;
    ev.code  = code;
    ev.value = value;
    write(fd, &ev, sizeof(ev));
}

static void syncEvents(int fd)
{
    emitEvent(fd, EV_SYN, SYN_REPORT, 0);
}


ch9350_mouse::ch9350_mouse(const QString &port, QObject *parent)
    : QThread(parent), m_port(port.toLocal8Bit().data())
{

}
ch9350_mouse::~ch9350_mouse()
{
    requestInterruption();
    wait();
    if (m_uinput >= 0) {
        ioctl(m_uinput, UI_DEV_DESTROY);
        close(m_uinput);
    }
}
void ch9350_mouse::run()
{
    m_uinput = openUinput();
    if (m_uinput < 0) return;
    int uart = UART_Open(-1, (char*)"/dev/ttyS10");
    if (uart == FALSE) {
        qDebug() << "无法打开串口设备";
        return;
    }
    UART_Init(uart, 115200, 0, 8, 1, 'N');
    if (uart < 0) {
        qWarning() << "CH9350: open failed";
        return;
    }

    char buf[64];
    QByteArray cache;
    while (!isInterruptionRequested()) {
        int n = UART_Recv(uart, buf, sizeof(buf));
        if (n <= 0) { msleep(5); continue; }

        //qDebug()<<"read" << n << "bytes:" << QByteArray(buf, n).toHex(' ');

        cache.append(buf, n);
        while (cache.size() >= 7) {
            int idx = cache.indexOf(QByteArray("\x57\xAB\x02", 3));
            if (idx < 0) { cache.clear(); continue; }
            if (cache.size() < idx + 7) break;

            const unsigned char *p = (unsigned char*)(cache.constData() + idx + 3);

            quint8 btn   = p[0];
            qint8  dx    = p[1];
            qint8  dy    = p[2];
            qint8  wheel = p[3];

            if (dx) emitEvent(m_uinput, EV_REL, REL_X, dx);
            if (dy) emitEvent(m_uinput, EV_REL, REL_Y, dy);
            if (wheel) emitEvent(m_uinput, EV_REL, REL_WHEEL, wheel);

            /* 按键事件 */
            static quint8 lastBtn = 0;
            if (btn != lastBtn) {
                emitEvent(m_uinput, EV_KEY, BTN_LEFT,   btn & 0x01);
                emitEvent(m_uinput, EV_KEY, BTN_RIGHT,  btn & 0x02);
                emitEvent(m_uinput, EV_KEY, BTN_MIDDLE, btn & 0x04);
                lastBtn = btn;
            }

            syncEvents(m_uinput);
            //qDebug() << "Btn" << QString::number(btn, 2) << "dx" << dx << "dy" << dy << "wheel" << wheel;

            cache.remove(0, idx + 10);
        }
    }
    UART_Close(uart);
}
