#ifndef CUSTOMMESSAGEBOX_H
#define CUSTOMMESSAGEBOX_H

#include "CustomDialog.h"
#include <QLabel>
#include <QPushButton>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QPixmap>
#include <QIcon>

class CustomMessageBox : public CustomDialog
{
    Q_OBJECT

public:
    enum Icon {
        NoIcon,
        Information,
        Warning,
        Critical,
        Question
    };

    enum StandardButton {
        NoButton = 0x00000000,
        Ok = 0x00000400,
        Save = 0x00000800,
        SaveAll = 0x00001000,
        Open = 0x00002000,
        Yes = 0x00004000,
        YesToAll = 0x00008000,
        No = 0x00010000,
        NoToAll = 0x00020000,
        Abort = 0x00040000,
        Retry = 0x00080000,
        Ignore = 0x00100000,
        Close = 0x00200000,
        Cancel = 0x00400000,
        Discard = 0x00800000,
        Help = 0x01000000,
        Apply = 0x02000000,
        Reset = 0x04000000,
        RestoreDefaults = 0x08000000
    };
    Q_DECLARE_FLAGS(StandardButtons, StandardButton)

    explicit CustomMessageBox(QWidget *parent = nullptr);
    explicit CustomMessageBox(Icon icon, const QString &title, const QString &text,
                              StandardButtons buttons = Ok, QWidget *parent = nullptr);
    ~CustomMessageBox();

    // 设置消息框内容
    void setText(const QString &text);
    void setInformativeText(const QString &text);
    void setIcon(Icon icon);
    void setStandardButtons(StandardButtons buttons);

    // 添加自定义按钮
    QPushButton* addButton(const QString &text, StandardButton button);
    QPushButton* addButton(StandardButton button);

    // 重写exec方法
    int exec() override;

    // 获取点击的按钮
    StandardButton clickedButton() const;

    // 静态便利方法
    static StandardButton information(QWidget *parent, const QString &title, const QString &text,
                                      StandardButtons buttons = Ok);
    static StandardButton warning(QWidget *parent, const QString &title, const QString &text,
                                  StandardButtons buttons = Ok);
    static StandardButton critical(QWidget *parent, const QString &title, const QString &text,
                                   StandardButtons buttons = Ok);
    static StandardButton question(QWidget *parent, const QString &title, const QString &text,
                                   StandardButtons buttons = StandardButtons(Yes | No));

private slots:
    void onButtonClicked();

protected:
    void showEvent(QShowEvent *event) override;

private:
    void setupUI();
    void setupIcon(Icon icon);
    QString getButtonText(StandardButton button);
    QPixmap getIconPixmap(Icon icon);
    QSize calculateOptimalSize();

    QVBoxLayout *m_contentLayout;
    QHBoxLayout *m_mainLayout;
    QVBoxLayout *m_textLayout;
    QHBoxLayout *m_buttonLayout;

    QLabel *m_iconLabel;
    QLabel *m_textLabel;
    QLabel *m_informativeLabel;

    StandardButton m_clickedButton;
    QList<QPushButton*> m_buttons;
    QMap<QPushButton*, StandardButton> m_buttonMap;

};

Q_DECLARE_OPERATORS_FOR_FLAGS(CustomMessageBox::StandardButtons)

#endif // CUSTOMMESSAGEBOX_H
