#include "CustomMessageBox.h"
#include <QApplication>
#include <QStyle>
#include <QScreen>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPushButton>
#include <QLabel>
#include <QShowEvent>
#include <QFontMetrics>

CustomMessageBox::CustomMessageBox(QWidget *parent)
    : CustomDialog(parent)
    , m_clickedButton(NoButton)
{
    setupUI();
}

CustomMessageBox::CustomMessageBox(Icon icon, const QString &title,
                                   const QString &text, StandardButtons buttons,
                                   QWidget *parent)
    : CustomMessageBox(parent)
{
    setDialogTitle(title);
    setText(text);
    setIcon(icon);
    setStandardButtons(buttons);
}

CustomMessageBox::~CustomMessageBox() = default;

void CustomMessageBox::setupUI()
{
    QWidget *contentWidget = new QWidget(this);
    m_contentLayout = new QVBoxLayout(contentWidget);
    m_contentLayout->setSpacing(20);
    m_contentLayout->setContentsMargins(20, 20, 20, 20);

    m_mainLayout = new QHBoxLayout();
    m_mainLayout->setSpacing(15);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);

    /* ---------- 图标 ---------- */
    m_iconLabel = new QLabel(contentWidget);
    m_iconLabel->setFixedSize(48, 48);
    m_iconLabel->hide();

    QWidget *iconContainer = new QWidget(contentWidget);
    QVBoxLayout *iconLayout = new QVBoxLayout(iconContainer);
    iconLayout->setContentsMargins(0, 0, 0, 0);
    iconLayout->setAlignment(Qt::AlignTop);
    iconLayout->addWidget(m_iconLabel);
    iconLayout->addStretch();

    /* ---------- 文本 ---------- */
    m_textLayout = new QVBoxLayout();
    m_textLayout->setSpacing(10);
    m_textLayout->setContentsMargins(0, 0, 0, 0);

    m_textLabel = new QLabel(contentWidget);
    m_textLabel->setWordWrap(true);
    m_textLabel->setAlignment(Qt::AlignLeft | Qt::AlignTop);
    m_textLabel->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    m_textLabel->setStyleSheet("font-size:14px;color:#333;background:transparent;");
    m_textLabel->setMaximumWidth(500); // 限制最大宽度

    m_informativeLabel = new QLabel(contentWidget);
    m_informativeLabel->setWordWrap(true);
    m_informativeLabel->setAlignment(Qt::AlignLeft | Qt::AlignTop);
    m_informativeLabel->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
    m_informativeLabel->setStyleSheet("font-size:12px;color:#666;background:transparent;");
    m_informativeLabel->setMaximumWidth(500); // 限制最大宽度
    m_informativeLabel->hide();

    m_textLayout->addWidget(m_textLabel);
    m_textLayout->addWidget(m_informativeLabel);
    m_textLayout->addStretch(1);

    m_mainLayout->addWidget(iconContainer);
    m_mainLayout->addLayout(m_textLayout, 1);

    /* ---------- 按钮 ---------- */
    m_buttonLayout = new QHBoxLayout();
    m_buttonLayout->setContentsMargins(0, 20, 0, 0);
    m_buttonLayout->addStretch();

    m_contentLayout->addLayout(m_mainLayout, 1);
    m_contentLayout->addLayout(m_buttonLayout);

    setDialogContent(contentWidget);
    setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Preferred);
}

void CustomMessageBox::showEvent(QShowEvent *event)
{
    CustomDialog::showEvent(event);

    // 计算并设置最佳大小
    QSize optimalSize = calculateOptimalSize();
    resize(optimalSize);

    // 确保窗口在屏幕范围内
    if (QScreen *screen = QGuiApplication::primaryScreen()) {
        QRect screenGeometry = screen->availableGeometry();
        QRect windowGeometry = geometry();

        // 如果窗口超出屏幕，调整位置
        if (windowGeometry.right() > screenGeometry.right()) {
            windowGeometry.moveRight(screenGeometry.right());
        }
        if (windowGeometry.bottom() > screenGeometry.bottom()) {
            windowGeometry.moveBottom(screenGeometry.bottom());
        }
        if (windowGeometry.left() < screenGeometry.left()) {
            windowGeometry.moveLeft(screenGeometry.left());
        }
        if (windowGeometry.top() < screenGeometry.top()) {
            windowGeometry.moveTop(screenGeometry.top());
        }

        setGeometry(windowGeometry);
    }
}

void CustomMessageBox::setText(const QString &text)
{
    if (m_textLabel) {
        m_textLabel->setText(text);
        // 如果窗口已经显示，重新计算大小
        if (isVisible()) {
            QSize optimalSize = calculateOptimalSize();
            resize(optimalSize);
        }
    }
}

void CustomMessageBox::setInformativeText(const QString &text)
{
    if (m_informativeLabel) {
        bool empty = text.isEmpty();
        m_informativeLabel->setText(text);
        m_informativeLabel->setVisible(!empty);
        // 如果窗口已经显示，重新计算大小
        if (isVisible()) {
            QSize optimalSize = calculateOptimalSize();
            resize(optimalSize);
        }
    }
}

void CustomMessageBox::setIcon(Icon icon)
{
    if (!m_iconLabel) return;
    if (icon == NoIcon) {
        m_iconLabel->hide();
    } else {
        QPixmap px = getIconPixmap(icon);
        m_iconLabel->setPixmap(px.scaled(48, 48, Qt::KeepAspectRatio, Qt::SmoothTransformation));
        m_iconLabel->show();
    }
}

void CustomMessageBox::setStandardButtons(StandardButtons buttons)
{
    for (QPushButton *btn : m_buttons) {
        m_buttonLayout->removeWidget(btn);
        delete btn;
    }
    m_buttons.clear();
    m_buttonMap.clear();

    if (buttons & Ok)     addButton(Ok);
    if (buttons & Yes)    addButton(Yes);
    if (buttons & No)     addButton(No);
    if (buttons & Cancel) addButton(Cancel);
    if (buttons & Save)   addButton(Save);
    if (buttons & Close)  addButton(Close);
    if (buttons & Abort)  addButton(Abort);
    if (buttons & Retry)  addButton(Retry);
    if (buttons & Ignore) addButton(Ignore);
}

QPushButton *CustomMessageBox::addButton(const QString &text, StandardButton button)
{
    QPushButton *btn = new QPushButton(text, this);
    btn->setFixedSize(80, 30);
    btn->setStyleSheet(
        "QPushButton{background:#f0f0f0;border:1px solid #ccc;border-radius:4px;font-size:12px;color:#333;}"
        "QPushButton:hover{background:#e0e0e0;border-color:#999;}"
        "QPushButton:pressed{background:#d0d0d0;}"
        );
    connect(btn, &QPushButton::clicked, this, &CustomMessageBox::onButtonClicked);
    m_buttons.append(btn);
    m_buttonMap[btn] = button;
    m_buttonLayout->addWidget(btn);
    return btn;
}

QPushButton *CustomMessageBox::addButton(StandardButton button)
{
    return addButton(getButtonText(button), button);
}

int CustomMessageBox::exec()
{
    m_clickedButton = NoButton;
    return CustomDialog::exec();
}

CustomMessageBox::StandardButton CustomMessageBox::clickedButton() const
{
    return m_clickedButton;
}

void CustomMessageBox::onButtonClicked()
{
    auto *btn = qobject_cast<QPushButton*>(sender());
    if (btn && m_buttonMap.contains(btn)) {
        m_clickedButton = m_buttonMap[btn];
        accept();
    }
}

QString CustomMessageBox::getButtonText(StandardButton button)
{
    switch (button) {
    case Ok: return "OK";
    case Yes: return "Yes";
    case No: return "No";
    case Cancel: return "Cancel";
    case Save: return "Save";
    case Close: return "Close";
    case Abort: return "Discontinue";
    case Retry: return "Retry";
    case Ignore: return "Ignore";
    default: return "OK";
    }
}

QPixmap CustomMessageBox::getIconPixmap(Icon icon)
{
    QStyle *style = QApplication::style();
    QStyle::StandardPixmap sp;
    switch (icon) {
    case Information: sp = QStyle::SP_MessageBoxInformation; break;
    case Warning:     sp = QStyle::SP_MessageBoxWarning;     break;
    case Critical:    sp = QStyle::SP_MessageBoxCritical;    break;
    case Question:    sp = QStyle::SP_MessageBoxQuestion;    break;
    default: return {};
    }
    return style->standardPixmap(sp);
}

/* -------------- 静态便利方法 -------------- */
CustomMessageBox::StandardButton CustomMessageBox::information(
    QWidget *parent, const QString &title,
    const QString &text, StandardButtons buttons)
{
    CustomMessageBox box(Information, title, text, buttons, parent);
    box.exec();
    return box.clickedButton();
}

CustomMessageBox::StandardButton CustomMessageBox::warning(
    QWidget *parent, const QString &title,
    const QString &text, StandardButtons buttons)
{
    CustomMessageBox box(Warning, title, text, buttons, parent);
    box.exec();
    return box.clickedButton();
}

CustomMessageBox::StandardButton CustomMessageBox::critical(
    QWidget *parent, const QString &title,
    const QString &text, StandardButtons buttons)
{
    CustomMessageBox box(Critical, title, text, buttons, parent);
    box.exec();
    return box.clickedButton();
}

CustomMessageBox::StandardButton CustomMessageBox::question(
    QWidget *parent, const QString &title,
    const QString &text, StandardButtons buttons)
{
    CustomMessageBox box(Question, title, text, buttons, parent);
    box.exec();
    return box.clickedButton();
}

QSize CustomMessageBox::calculateOptimalSize()
{
    // 确保布局已经计算
    layout()->activate();

    // 获取屏幕信息
    QScreen *screen = QGuiApplication::primaryScreen();
    if (!screen) {
        return QSize(400, 200); // 默认大小
    }

    QRect screenGeometry = screen->availableGeometry();
    int maxWidth = static_cast<int>(screenGeometry.width() * 0.5);  // 最大宽度为屏幕的50%
    int maxHeight = static_cast<int>(screenGeometry.height() * 0.8); // 最大高度为屏幕的80%

    // 设置一个合理的目标宽度范围
    int minContentWidth = 200;  // 最小内容宽度
    int maxContentWidth = 500;  // 最大内容宽度，避免过宽
    int preferredWidth = 350;   // 首选宽度

    // 计算文本所需的宽度
    int textWidth = 0;
    int textHeight = 0;

    if (m_textLabel && !m_textLabel->text().isEmpty()) {
        QFontMetrics fm(m_textLabel->font());
        QString text = m_textLabel->text();

        // 计算单行文本宽度
        int singleLineWidth = fm.horizontalAdvance(text);

        // 如果文本很短，使用实际宽度
        if (singleLineWidth <= preferredWidth) {
            textWidth = singleLineWidth;
            textHeight = fm.height();
        } else {
            // 文本较长时，使用首选宽度进行换行
            int targetWidth = qMin(maxContentWidth, qMax(preferredWidth, minContentWidth));
            QRect boundingRect = fm.boundingRect(0, 0, targetWidth, 0,
                                               Qt::TextWordWrap | Qt::AlignLeft, text);
            textWidth = targetWidth;
            textHeight = boundingRect.height();
        }
    }

    // 计算详细信息文本所需的大小
    int informativeWidth = 0;
    int informativeHeight = 0;

    if (m_informativeLabel && !m_informativeLabel->text().isEmpty() && m_informativeLabel->isVisible()) {
        QFontMetrics fm(m_informativeLabel->font());
        QString text = m_informativeLabel->text();

        int singleLineWidth = fm.horizontalAdvance(text);

        // 对于详细信息，也使用相同的宽度策略
        if (singleLineWidth <= preferredWidth) {
            informativeWidth = singleLineWidth;
            informativeHeight = fm.height();
        } else {
            int targetWidth = qMin(maxContentWidth, qMax(preferredWidth, minContentWidth));
            QRect boundingRect = fm.boundingRect(0, 0, targetWidth, 0,
                                               Qt::TextWordWrap | Qt::AlignLeft, text);
            informativeWidth = targetWidth;
            informativeHeight = boundingRect.height();
        }
    }

    // 计算内容区域所需的宽度和高度
    // 使用文本和详细信息中较宽的那个，但不超过最大内容宽度
    int contentWidth = qMin(maxContentWidth, qMax(textWidth, informativeWidth));
    int contentHeight = textHeight + informativeHeight;

    // 如果有详细信息，添加间距
    if (informativeHeight > 0) {
        contentHeight += 10; // 文本之间的间距
    }

    // 添加图标宽度（如果有图标）
    if (m_iconLabel && m_iconLabel->isVisible()) {
        contentWidth += 48 + 15; // 图标宽度 + 间距
        contentHeight = qMax(contentHeight, 48); // 确保高度至少能容纳图标
    }

    // 添加按钮区域高度
    if (!m_buttons.isEmpty()) {
        contentHeight += 30 + 20; // 按钮高度 + 上边距
    }

    // 添加内容区域的边距
    contentWidth += 40;  // 左右边距
    contentHeight += 40; // 上下边距

    // 添加标题栏高度
    contentHeight += 35;

    // 设置最小和最大尺寸
    int minWidth = 280;   // 减小最小宽度
    int minHeight = 150;
    int maxFinalWidth = qMin(maxWidth, 600);  // 限制最大宽度为600px

    // 计算最终尺寸
    int finalWidth = qMax(minWidth, qMin(contentWidth, maxFinalWidth));
    int finalHeight = qMax(minHeight, qMin(contentHeight, maxHeight));

    return QSize(finalWidth, finalHeight);
}
