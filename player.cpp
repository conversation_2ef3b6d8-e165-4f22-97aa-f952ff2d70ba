#include "player.h"
#include "ui_player.h"
#include <QDebug>
#include <QApplication>
#include <QScreen>
#include <QEvent>
player::player(QWidget *parent, const QString &videoPath)
    : QMainWindow(parent)
    , ui(new Ui::player)
    , pipeline(nullptr)
    , playbin(nullptr)
    , bus(nullptr)
    , positionTimer(new QTimer(this))
    , hideControlsTimer(new QTimer(this))
    , messageTimer(new QTimer(this))
    , isPlaying(false)
    , isSliderPressed(false)
    , duration(0)
    , controlsVisible(true)
{
    ui->setupUi(this);
    setWindowFlags(Qt::FramelessWindowHint);
    setAttribute(Qt::WA_TranslucentBackground);
    showMaximized();

    // 初始化GStreamer
    initializeGStreamer();

    // 设置信号连接
    setupConnections();

    // 设置鼠标追踪
    setMouseTracking(true);

    // 为所有子控件也设置鼠标追踪
    ui->centralwidget->setMouseTracking(true);
    ui->videoWidget->setMouseTracking(true);

    // 安装事件过滤器来捕获所有鼠标移动事件
    ui->centralwidget->installEventFilter(this);
    ui->videoWidget->installEventFilter(this);
    this->installEventFilter(this);

    // 设置焦点策略以接收键盘事件
    setFocusPolicy(Qt::StrongFocus);

    // 配置隐藏控制条定时器
    hideControlsTimer->setSingleShot(true);
    hideControlsTimer->setInterval(2000); // 2秒后隐藏
    connect(hideControlsTimer, &QTimer::timeout, this, &player::hideControls);

    // 配置GStreamer消息检查定时器
    messageTimer->setInterval(100); // 每100ms检查一次消息
    connect(messageTimer, &QTimer::timeout, this, &player::checkGStreamerMessages);

    // 加载视频文件
    QString defaultPath = "file:///tmp/recordings/recording_2021-01-01_13-00-12.mp4";
    QString pathToLoad = videoPath.isEmpty() ? defaultPath : videoPath;

    // 如果传入的路径不是以file://开头，则添加file://前缀
    if (!pathToLoad.startsWith("file://")) {
        pathToLoad = "file://" + pathToLoad;
    }

    loadVideo(pathToLoad);

    // 启动隐藏控制条定时器
    resetHideTimer();
}

player::~player()
{
    if (messageTimer) {
        messageTimer->stop();
    }
    if (pipeline) {
        gst_element_set_state(pipeline, GST_STATE_NULL);
        gst_object_unref(pipeline);
    }
    if (bus) {
        gst_object_unref(bus);
    }
    delete ui;
}
void player::initializeGStreamer()
{
    // 初始化GStreamer
    gst_init(nullptr, nullptr);
    // 主屏幕
    QScreen *screen = QGuiApplication::primaryScreen();

    // 整个屏幕的完整几何（含任务栏、Dock、菜单栏等）
    QRect full = screen->geometry();
    qDebug() << "屏幕完整区域：" << full;

    // 屏幕的“可用”几何（去掉系统任务栏/Dock后的区域）
    QRect avail = screen->availableGeometry();
    qDebug() << "可用区域：" << avail;

    int x = avail.x();
    int y = avail.y();
    int w = avail.width();
    int h = avail.height();
    printf("x=%d,y=%d,w=%d,h=%d\n",x,y,w,h);
    // 定义矩形坐标和尺寸
    gint rect[4] = {x, y, w, h};  // x, y, width, height
    // 初始化GValue为GstValueArray类型
    GValue val = G_VALUE_INIT;
    g_value_init(&val, GST_TYPE_ARRAY);

    // 添加四个整数值到数组中RK
    for (int i = 0; i < 4; i++) {
        GValue elem = G_VALUE_INIT;
        g_value_init(&elem, G_TYPE_INT);
        g_value_set_int(&elem, rect[i]);
        gst_value_array_append_value(&val, &elem);
        g_value_unset(&elem);
    }

    // 创建playbin pipeline
    pipeline = gst_element_factory_make("playbin", "player");
    if (!pipeline) {
        qDebug() << "Failed to create playbin element";
        return;
    }
    // 设置video-sink为waylandsink并配置显示区域
    GstElement *videoSink = gst_element_factory_make("waylandsink", "video-sink");
    if (videoSink) {
        // 设置waylandsink的显示位置和大小
        g_object_set_property(G_OBJECT(videoSink), "render-rectangle", &val);
        g_object_set(pipeline, "video-sink", videoSink, nullptr);
    }

    // 获取bus用于消息处理
    bus = gst_element_get_bus(pipeline);

    // 设置定时器更新播放位置
    positionTimer->setInterval(100); // 每100ms更新一次
    connect(positionTimer, &QTimer::timeout, this, &player::updatePosition);
}

void player::setupConnections()
{
    // 连接UI控件信号
    connect(ui->playPauseButton, &QPushButton::clicked, this, &player::onPlayPauseClicked);
    connect(ui->rewindButton, &QPushButton::clicked, this, &player::onRewindClicked);
    connect(ui->fastForwardButton, &QPushButton::clicked, this, &player::onFastForwardClicked);
    connect(ui->exit_Button, &QPushButton::clicked, this, &player::onExitClicked);

    // 连接进度条信号
    connect(ui->progressSlider, &QSlider::sliderPressed, this, &player::onProgressSliderPressed);
    connect(ui->progressSlider, &QSlider::sliderReleased, this, &player::onProgressSliderReleased);
    connect(ui->progressSlider, &QSlider::valueChanged, this, &player::onProgressSliderValueChanged);
}

void player::loadVideo(const QString &filePath)
{
    if (!pipeline) {
        qDebug() << "Pipeline not initialized";
        return;
    }

    // 设置URI
    g_object_set(pipeline, "uri", filePath.toUtf8().constData(), nullptr);

    // 设置为PAUSED状态以获取媒体信息
    gst_element_set_state(pipeline, GST_STATE_PAUSED);

    // 等待状态改变完成
    GstStateChangeReturn ret = gst_element_get_state(pipeline, nullptr, nullptr, GST_CLOCK_TIME_NONE);
    if (ret == GST_STATE_CHANGE_FAILURE) {
        qDebug() << "Failed to set pipeline to PAUSED state";
        return;
    }

    // 获取视频时长
    if (gst_element_query_duration(pipeline, GST_FORMAT_TIME, &duration)) {
        qDebug() << "Video duration:" << duration / GST_SECOND << "seconds";
        // 设置进度条最大值
        ui->progressSlider->setMaximum(duration / GST_MSECOND);
    }

    // 更新时间标签
    updateTimeLabel(0, duration);

    // 自动开始播放
    gst_element_set_state(pipeline, GST_STATE_PLAYING);
    positionTimer->start();
    messageTimer->start(); // 启动消息检查定时器
    isPlaying = true;
    ui->playPauseButton->setChecked(false); // 播放时显示暂停图标
}

void player::onPlayPauseClicked()
{
    if (!pipeline) return;

    resetHideTimer(); // 重置隐藏定时器

    if (isPlaying) {
        // 暂停播放
        gst_element_set_state(pipeline, GST_STATE_PAUSED);
        positionTimer->stop();
        isPlaying = false;
        ui->playPauseButton->setChecked(true); // 暂停时显示播放图标
    } else {
        // 开始播放
        gst_element_set_state(pipeline, GST_STATE_PLAYING);
        positionTimer->start();
        isPlaying = true;
        ui->playPauseButton->setChecked(false); // 播放时显示暂停图标
    }
}

void player::onRewindClicked()
{
    if (!pipeline) return;

    resetHideTimer(); // 重置隐藏定时器

    gint64 current;
    if (gst_element_query_position(pipeline, GST_FORMAT_TIME, &current)) {
        gint64 newPos = current - 10 * GST_SECOND; // 后退10秒
        if (newPos < 0) newPos = 0;
        seek(newPos);

        // 立即更新进度条和时间显示
        ui->progressSlider->setValue(newPos / GST_MSECOND);
        updateTimeLabel(newPos, duration);
    }
}

void player::onFastForwardClicked()
{
    if (!pipeline) return;

    resetHideTimer(); // 重置隐藏定时器

    gint64 current;
    if (gst_element_query_position(pipeline, GST_FORMAT_TIME, &current)) {
        gint64 newPos = current + 10 * GST_SECOND; // 前进10秒
        if (newPos > duration) newPos = duration;
        seek(newPos);

        // 立即更新进度条和时间显示
        ui->progressSlider->setValue(newPos / GST_MSECOND);
        updateTimeLabel(newPos, duration);
    }
}

void player::onExitClicked()
{
    this->close();
}

void player::onProgressSliderPressed()
{
    isSliderPressed = true;
    resetHideTimer(); // 重置隐藏定时器
}

void player::onProgressSliderReleased()
{
    isSliderPressed = false;
    resetHideTimer(); // 重置隐藏定时器

    if (!pipeline) return;

    // 根据滑块位置跳转
    gint64 seekPos = (gint64)ui->progressSlider->value() * GST_MSECOND;
    seek(seekPos);
}

void player::onProgressSliderValueChanged(int value)
{
    if (isSliderPressed && duration > 0) {
        // 只在用户拖动时更新时间显示
        gint64 currentPos = (gint64)value * GST_MSECOND;
        updateTimeLabel(currentPos, duration);
    }
}

void player::updatePosition()
{
    if (!pipeline || isSliderPressed) return;

    gint64 current;
    if (gst_element_query_position(pipeline, GST_FORMAT_TIME, &current)) {
        // 更新进度条
        ui->progressSlider->setValue(current / GST_MSECOND);

        // 更新时间标签
        updateTimeLabel(current, duration);
    }
}

void player::updateTimeLabel(gint64 current, gint64 total)
{
    QString currentTime = formatTime(current);
    QString totalTime = formatTime(total);
    ui->timeLabel->setText(QString("%1/%2").arg(currentTime, totalTime));
}

QString player::formatTime(gint64 time)
{
    gint64 seconds = time / GST_SECOND;
    gint64 minutes = seconds / 60;
    seconds = seconds % 60;

    return QString("%1:%2").arg(minutes, 2, 10, QChar('0')).arg(seconds, 2, 10, QChar('0'));
}

void player::seek(gint64 position)
{
    if (!pipeline) return;

    gst_element_seek_simple(pipeline, GST_FORMAT_TIME,
                            (GstSeekFlags)(GST_SEEK_FLAG_FLUSH | GST_SEEK_FLAG_KEY_UNIT),
                            position);

    // 强制立即更新一次位置（因为seek是异步的，可能需要一点时间）
    QTimer::singleShot(50, this, [this]() {
        if (!isSliderPressed) {
            gint64 current;
            if (gst_element_query_position(pipeline, GST_FORMAT_TIME, &current)) {
                ui->progressSlider->setValue(current / GST_MSECOND);
                updateTimeLabel(current, duration);
            }
        }
    });
}

void player::showControls()
{
    if (!controlsVisible) {
        qDebug() << "Showing controls"; // 调试信息
        ui->progressSlider->show();
        ui->rewindButton->show();
        ui->playPauseButton->show();
        ui->fastForwardButton->show();
        ui->timeLabel->show();
        ui->exit_Button->show();
        controlsVisible = true;
    }
}

void player::hideControls()
{
    if (controlsVisible) {
        ui->progressSlider->hide();
        ui->rewindButton->hide();
        ui->playPauseButton->hide();
        ui->fastForwardButton->hide();
        ui->timeLabel->hide();
        ui->exit_Button->hide();
        controlsVisible = false;
    }
}

void player::resetHideTimer()
{
    showControls();
    hideControlsTimer->start();
}

void player::keyPressEvent(QKeyEvent *event)
{
    resetHideTimer(); // 键盘操作时显示控制条

    switch (event->key()) {
    case Qt::Key_Space:
        onPlayPauseClicked();
        break;
    case Qt::Key_Left:
        onRewindClicked();
        break;
    case Qt::Key_Right:
        onFastForwardClicked();
        break;
    case Qt::Key_Escape:
    case Qt::Key_Q:
        onExitClicked();
        break;
    default:
        QMainWindow::keyPressEvent(event);
        break;
    }
}

void player::mouseMoveEvent(QMouseEvent *event)
{
    resetHideTimer(); // 鼠标移动时显示控制条
    QMainWindow::mouseMoveEvent(event);
}

bool player::eventFilter(QObject *obj, QEvent *event)
{
    if (event->type() == QEvent::MouseMove) {
        resetHideTimer();
        return false; // 继续传递事件
    }
    return QMainWindow::eventFilter(obj, event);
}

void player::checkGStreamerMessages()
{
    if (!bus) return;

    GstMessage *msg;
    while ((msg = gst_bus_pop(bus)) != nullptr) {
        switch (GST_MESSAGE_TYPE(msg)) {
        case GST_MESSAGE_EOS:
            qDebug() << "End of stream reached, closing player";
            // 播放完成，关闭播放器窗口
            this->close();
            break;
        case GST_MESSAGE_ERROR:
        {
            GError *error;
            gchar *debug;
            gst_message_parse_error(msg, &error, &debug);
            qDebug() << "GStreamer error:" << error->message;
            g_error_free(error);
            g_free(debug);
            // 发生错误也关闭播放器窗口
            this->close();
        }
        break;
        default:
            break;
        }
        gst_message_unref(msg);
    }
}
