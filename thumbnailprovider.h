#ifndef THUMBNAILPROVIDER_H
#define THUMBNAILPROVIDER_H

#include <QFileIconProvider>
#include <QFileInfo>
#include <QPixmap>
#include <QIcon>
#include <QDebug>

class ThumbnailProvider : public QFileIconProvider
{
public:
    ThumbnailProvider(){};

    QIcon icon(const QFileInfo &info) const override {
        QString suffix = info.suffix().toLower();
        if (suffix == "mp4" || suffix == "avi" || suffix == "mkv") {
            // 假设外部生成好的缩略图路径如下
            QString thumbPath = "/data/thumb/.thumb_" + info.completeBaseName() + ".jpg";

            if (QFile::exists(thumbPath)) {
                // qDebug() << "[ThumbnailProvider] Load thumbnail:" << thumbPath;
                return QIcon(QPixmap(thumbPath));
            } 
            // else {
            //     qWarning() << "[ThumbnailProvider] Thumbnail not found for" << info.fileName();
            // }
        }

        // 默认图标处理
        return QFileIconProvider::icon(info);
    }
};

#endif // THUMBNAILPROVIDER_H

