QT       += core gui

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17
CONFIG += feature-wayland
# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    CustomDialog.cpp \
    CustomMessageBox.cpp \
    camera_param.cpp \
    camerastream.cpp \
    ch9350_mouse.cpp \
    main.cpp \
    mainwindow.cpp \
    player.cpp \
    uart.c

HEADERS += \
    CustomDialog.h \
    CustomMessageBox.h \
    camera_param.h \
    camerastream.h \
    ch9350_mouse.h \
    mainwindow.h \
    player.h \
    thumbnailprovider.h \
    uart.h

FORMS += \
    mainwindow.ui \
    player.ui

unix {
    GSTREAMER_DIR = /home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576
    GST_LIB_DIR = /home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host

    XKBCOMMON_DIR= /home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/lib

}

INCLUDEPATH += $$GSTREAMER_DIR/build/gstreamer1-1.22.9
INCLUDEPATH += $$GSTREAMER_DIR/build/gstreamer1-1.22.9/build
INCLUDEPATH += $$GSTREAMER_DIR/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0
INCLUDEPATH += $$GSTREAMER_DIR/target/usr/lib/gstreamer-1.0
INCLUDEPATH += $$GSTREAMER_DIR/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa
INCLUDEPATH += $$GSTREAMER_DIR/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11
INCLUDEPATH += $$GSTREAMER_DIR/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11
INCLUDEPATH += $$GSTREAMER_DIR/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore
INCLUDEPATH += $$GSTREAMER_DIR/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore/qpa
INCLUDEPATH += $$GSTREAMER_DIR/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5

INCLUDEPATH += $$GST_LIB_DIR/lib/glib-2.0/include
INCLUDEPATH += $$GST_LIB_DIR/include/glib-2.0

# 添加库路径和链接选项
LIBS += -L$$GST_LIB_DIR \
        -lgstreamer-1.0 \
        -lgstbase-1.0 \
        -lgstvideo-1.0 \
        -lgstapp-1.0 \
        -lgobject-2.0 \
        -lglib-2.0 \
        -lgmodule-2.0 \
        -lgstvideo-1.0\
        -ludev

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

RESOURCES += \
    icons.qrc
